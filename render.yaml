services:
  - type: web
    name: seo-analyzer
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn api_refactored:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: API_HOST
        value: 0.0.0.0
      - key: API_PORT
        fromService:
          type: web
          name: seo-analyzer
          property: port
      - key: PYTHON_VERSION
        value: 3.11.0
