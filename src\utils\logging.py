"""
Logging configuration and utilities
"""
import logging
import sys
from typing import Optional

from src.config.settings import settings


def setup_logging(level: Optional[str] = None, format_str: Optional[str] = None) -> None:
    """Setup application logging configuration"""
    log_level = level or settings.log_level
    log_format = format_str or settings.log_format
    
    # Convert string level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Configure root logger
    logging.basicConfig(
        level=numeric_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('seo_analysis.log', mode='a')
        ]
    )
    
    # Set specific loggers to appropriate levels
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('googleapiclient').setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for the given name"""
    return logging.getLogger(name)


# Initialize logging when module is imported
setup_logging()
