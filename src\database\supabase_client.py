"""
Supabase database client
"""
import hashlib
import pandas as pd
from datetime import datetime
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse

from src.utils.logging import get_logger


class SupabaseError(Exception):
    """Base exception for Supabase operations"""
    pass


class ForeignKeyConstraintError(SupabaseError):
    """Raised when a foreign key constraint prevents an operation"""
    def __init__(self, message: str, table: str = None, constraint: str = None):
        super().__init__(message)
        self.table = table
        self.constraint = constraint


class SiteDeletionError(SupabaseError):
    """Raised when site deletion fails"""
    def __init__(self, message: str, site_id: str = None, remaining_refs: Dict[str, int] = None):
        super().__init__(message)
        self.site_id = site_id
        self.remaining_refs = remaining_refs or {}

logger = get_logger(__name__)

try:
    from supabase import create_client
    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("Supabase client not available. Install with: pip install supabase")
    SUPABASE_AVAILABLE = False


class SupabaseClient:
    """Client for Supabase database operations"""
    
    def __init__(self, url: str, key: str, domain: str):
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase client not available")
        
        self.client = create_client(url, key)
        self.domain = domain
        self.site_id = self._get_or_create_site_id()
        self.db_id = self.site_id
    
    def _get_or_create_site_id(self) -> str:
        """Get or create a site ID for the domain"""
        # Check if site exists
        response = self.client.table('sites').select('id').eq('domain', self.domain).execute()

        if response.data:
            return response.data[0]['id']

        # Create new site - let database auto-generate the ID
        site_data = {
            'domain': self.domain,
            'created_at': datetime.now().isoformat()
        }

        response = self.client.table('sites').insert(site_data).execute()

        if response.data and len(response.data) > 0:
            return response.data[0]['id']
        else:
            raise Exception(f"Failed to create site for domain {self.domain}")

    def save_site_configuration(self, domain_property: str, ga_property_id: str,
                               service_account_data: dict, homepage: str = None, wp_api_key: str = None) -> tuple[bool, str]:
        """Save site configuration for future re-analysis"""
        try:
            config_data = {
                'domain_property': domain_property,
                'ga_property_id': ga_property_id,
                'service_account_data': service_account_data,
                'homepage': homepage,
                'wp_api_key': wp_api_key,
                'last_updated': datetime.now().isoformat()
            }

            logger.info(f"Attempting to save configuration for site {self.domain} (ID: {self.site_id})")

            # Update the site record with configuration
            response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()

            if response.data is not None and len(response.data) > 0:
                logger.info(f"Successfully saved configuration for site {self.domain}")
                return True, "Configuration saved successfully"
            elif response.data is not None and len(response.data) == 0:
                logger.error(f"No rows updated for site {self.domain} - site may not exist")
                return False, f"Site with ID {self.site_id} not found"
            else:
                logger.error(f"Unexpected response from Supabase: {response}")
                return False, "Unexpected response from database"

        except Exception as e:
            error_msg = f"Error saving site configuration: {str(e)}"
            logger.error(error_msg)
            logger.exception("Full exception details:")
            return False, error_msg

    @classmethod
    def create_site_with_config(cls, url: str, key: str, domain_property: str,
                               ga_property_id: str, service_account_data: dict,
                               homepage: str = None, wp_api_key: str = None) -> tuple[bool, str, str]:
        """Create a new site with configuration (no analysis)"""
        try:
            from urllib.parse import urlparse
            from supabase import create_client

            domain = urlparse(domain_property).netloc

            # Create direct client connection (don't use SupabaseClient constructor)
            client = create_client(url, key)

            # Check if site already exists
            existing_response = client.table('sites').select('id, domain').eq('domain', domain).execute()

            if existing_response.data:
                return False, f"Site {domain} already exists", str(existing_response.data[0]['id'])

            # Create site with configuration - let database auto-generate ID
            site_data = {
                'domain': domain,
                'domain_property': domain_property,
                'ga_property_id': ga_property_id,
                'service_account_data': service_account_data,
                'homepage': homepage,
                'wp_api_key': wp_api_key,
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            }

            response = client.table('sites').insert(site_data).execute()

            if response.data and len(response.data) > 0:
                site_id = str(response.data[0]['id'])
                logger.info(f"Created site {domain} with configuration (ID: {site_id})")
                return True, f"Site {domain} created successfully", site_id
            else:
                logger.error(f"Failed to create site {domain}")
                return False, f"Failed to create site {domain}", None

        except Exception as e:
            logger.error(f"Error creating site with configuration: {e}")
            return False, f"Error creating site: {str(e)}", None

    def get_site_configuration(self) -> Optional[dict]:
        """Get stored site configuration"""
        try:
            response = self.client.table('sites').select('domain_property, ga_property_id, service_account_data, homepage, wp_api_key').eq('id', self.site_id).execute()

            if response.data and response.data[0].get('domain_property'):
                config = response.data[0]
                logger.info(f"Retrieved configuration for site {self.domain}")
                return config
            else:
                logger.warning(f"No configuration found for site {self.domain}")
                return None

        except Exception as e:
            logger.error(f"Error retrieving site configuration: {e}")
            return None

    def update_site_configuration(self, domain_property: str, ga_property_id: str,
                                 service_account_data: dict, homepage: str = None, wp_api_key: str = None) -> tuple[bool, str]:
        """Update existing site configuration"""
        try:
            config_data = {
                'domain_property': domain_property,
                'ga_property_id': ga_property_id,
                'service_account_data': service_account_data,
                'homepage': homepage,
                'wp_api_key': wp_api_key,
                'last_updated': datetime.now().isoformat()
            }

            logger.info(f"Attempting to update configuration for site {self.domain} (ID: {self.site_id})")
            logger.debug(f"Update data: {config_data}")

            # Update the site record with new configuration
            response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()

            logger.debug(f"Supabase response: {response}")

            # Check if the update was successful
            if response.data is not None and len(response.data) > 0:
                logger.info(f"Successfully updated configuration for site {self.domain}")
                return True, "Configuration updated successfully"
            elif response.data is not None and len(response.data) == 0:
                logger.error(f"No rows updated for site {self.domain} - site may not exist")
                return False, f"Site with ID {self.site_id} not found or no changes made"
            else:
                logger.error(f"Unexpected response from Supabase: {response}")
                return False, "Unexpected response from database"

        except Exception as e:
            error_msg = f"Error updating site configuration: {str(e)}"
            logger.error(error_msg)
            logger.exception("Full exception details:")
            return False, error_msg

    def delete_all_site_data(self) -> bool:
        """Delete all data for this site from all tables"""
        try:
            tables_to_clean = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
            deleted_counts = {}

            for table in tables_to_clean:
                try:
                    # Get count before deletion
                    count_response = self.client.table(table).select('id').eq('site_id', self.site_id).execute()
                    count = len(count_response.data) if count_response.data else 0

                    if count > 0:
                        # Delete all records for this site
                        delete_response = self.client.table(table).delete().eq('site_id', self.site_id).execute()
                        deleted_counts[table] = count
                        logger.info(f"Deleted {count} records from {table} for site {self.domain}")
                    else:
                        deleted_counts[table] = 0

                except Exception as table_error:
                    logger.error(f"Error deleting from table {table}: {table_error}")
                    deleted_counts[table] = f"Error: {table_error}"

            # Update site record to update last_updated but KEEP the configuration
            try:
                # First, let's check what the current site data looks like
                current_site = self.client.table('sites').select('*').eq('id', self.site_id).execute()
                logger.info(f"Current site data before update: {current_site.data}")

                update_response = self.client.table('sites').update({
                    'last_updated': datetime.now().isoformat()
                }).eq('id', self.site_id).execute()

                logger.info(f"Site update response: {update_response.data}")
                logger.info(f"Updated last_updated timestamp for site {self.domain} (configuration preserved)")

                # Verify the site data after update
                updated_site = self.client.table('sites').select('*').eq('id', self.site_id).execute()
                logger.info(f"Site data after update: {updated_site.data}")

            except Exception as config_error:
                logger.error(f"Error updating site timestamp: {config_error}")

            logger.info(f"Site data deletion summary for {self.domain}: {deleted_counts}")
            return True

        except Exception as e:
            logger.error(f"Error deleting site data: {e}")
            return False

    def check_site_references(self) -> Dict[str, int]:
        """Check how many records reference this site in each table"""
        tables_to_check = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
        reference_counts = {}

        for table in tables_to_check:
            try:
                count_response = self.client.table(table).select('id', count='exact').eq('site_id', self.site_id).execute()
                reference_counts[table] = count_response.count or 0
            except Exception as e:
                logger.error(f"Error checking references in {table}: {e}")
                reference_counts[table] = -1  # -1 indicates error

        return reference_counts

    def delete_site_completely(self) -> bool:
        """Delete site and all its data completely"""
        try:
            # First check what references exist
            reference_counts = self.check_site_references()
            total_references = sum(count for count in reference_counts.values() if count > 0)

            logger.info(f"Starting complete deletion for site {self.domain} (ID: {self.site_id})")
            logger.info(f"Reference counts: {reference_counts}")
            logger.info(f"Total records to delete: {total_references}")

            # Delete data from all child tables first (in correct order to avoid FK constraints)
            tables_to_clean = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
            deleted_counts = {}

            for table in tables_to_clean:
                try:
                    # Get count before deletion
                    count_response = self.client.table(table).select('id').eq('site_id', self.site_id).execute()
                    count = len(count_response.data) if count_response.data else 0

                    if count > 0:
                        # Delete all records for this site
                        delete_response = self.client.table(table).delete().eq('site_id', self.site_id).execute()
                        deleted_counts[table] = count
                        logger.info(f"Deleted {count} records from {table} for site {self.domain}")
                    else:
                        deleted_counts[table] = 0
                        logger.info(f"No records to delete from {table} for site {self.domain}")

                except Exception as table_error:
                    logger.error(f"Error deleting from table {table}: {table_error}")
                    deleted_counts[table] = f"Error: {table_error}"
                    # Continue with other tables even if one fails

            # Now delete the site record itself
            logger.info(f"Deleting site record for {self.domain} (ID: {self.site_id})")
            response = self.client.table('sites').delete().eq('id', self.site_id).execute()

            # Check if deletion was successful
            # Supabase returns an empty list for successful deletes, or the deleted records
            logger.info(f"Site deletion response: {response}")

            # Verify the site is actually deleted by trying to fetch it
            verify_response = self.client.table('sites').select('id').eq('id', self.site_id).execute()

            if not verify_response.data:  # Empty list means site was deleted
                logger.info(f"✅ Successfully deleted site {self.domain} completely")
                logger.info(f"Deletion summary: {deleted_counts}")
                return True
            else:
                logger.error(f"❌ Site {self.domain} still exists after deletion attempt")
                return False

        except Exception as e:
            error_str = str(e)
            logger.error(f"Error completely deleting site {self.domain}: {e}")
            logger.exception("Full exception details:")

            # Check for specific foreign key constraint errors
            if "foreign key constraint" in error_str.lower():
                logger.error("❌ Foreign key constraint violation detected!")
                logger.error("This usually means some child records were not properly deleted.")

                # Re-check references to see what's left
                remaining_refs = self.check_site_references()
                logger.error(f"Remaining references after deletion attempt: {remaining_refs}")

                # Raise a more specific exception
                raise SiteDeletionError(
                    f"Cannot delete site {self.domain} due to foreign key constraints. "
                    f"Remaining references: {remaining_refs}",
                    site_id=str(self.site_id),
                    remaining_refs=remaining_refs
                ) from e

            return False

    def force_delete_site(self) -> bool:
        """Force delete site by clearing all data first, then deleting site record"""
        try:
            logger.info(f"🔥 Force deleting site {self.domain} (ID: {self.site_id})")

            # Step 1: Get initial reference counts
            initial_refs = self.check_site_references()
            logger.info(f"Initial references: {initial_refs}")

            # Step 2: Force delete from all tables (ignore errors)
            tables_to_clean = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']

            for table in tables_to_clean:
                try:
                    # Delete all records for this site, ignoring any errors
                    self.client.table(table).delete().eq('site_id', self.site_id).execute()
                    logger.info(f"✅ Force deleted from {table}")
                except Exception as e:
                    logger.warning(f"⚠️ Error force deleting from {table}: {e}")
                    # Continue anyway

            # Step 3: Check remaining references
            remaining_refs = self.check_site_references()
            logger.info(f"Remaining references after force delete: {remaining_refs}")

            # Step 4: Try to delete the site record
            try:
                response = self.client.table('sites').delete().eq('id', self.site_id).execute()
                logger.info(f"Site deletion response: {response}")

                # Verify deletion
                verify_response = self.client.table('sites').select('id').eq('id', self.site_id).execute()

                if not verify_response.data:
                    logger.info(f"✅ Successfully force deleted site {self.domain}")
                    return True
                else:
                    logger.error(f"❌ Site {self.domain} still exists after force deletion")
                    return False

            except Exception as site_delete_error:
                logger.error(f"❌ Failed to delete site record: {site_delete_error}")
                return False

        except Exception as e:
            logger.error(f"❌ Force deletion failed for site {self.domain}: {e}")
            logger.exception("Full exception details:")
            return False
    
    def save_pages_data(self, df: pd.DataFrame) -> Optional[Any]:
        """Save pages data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')

        # Add URL hash if it doesn't exist
        if 'url_hash' not in df.columns:
            df['url_hash'] = df['URL'].apply(lambda x: hashlib.md5(x.encode()).hexdigest())

        # Filter to only include columns that exist in the database schema
        valid_columns = [
            'URL', 'SEO Title', 'Meta Description', 'H1', 'Page Content',
            'Focus Keyword', 'Page Type', 'Topic', 'Title Length',
            'GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views',
            'url_hash', 'raw_html', 'site_id', 'snapshot_date'
        ]
        df = df[[col for col in valid_columns if col in df.columns]]

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('pages').upsert(
                records,
                on_conflict='site_id,URL,snapshot_date'
            ).execute()

            logger.info(f"Saved {len(records)} pages to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving pages data: {e}")
            return None
    
    def save_gsc_keywords(self, df: pd.DataFrame) -> Optional[Any]:
        """Save GSC keywords data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id

        # Add keyword hash
        if 'keyword_hash' not in df.columns:
            df['keyword_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('Keyword', '')}{row.get('URL', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Process in batches of 1000 records to avoid timeout
        batch_size = 1000
        total_records = len(df)
        total_saved = 0

        for i in range(0, total_records, batch_size):
            batch_df = df.iloc[i:i+batch_size]
            records = batch_df.to_dict(orient='records')

            try:
                response = self.client.table('gsc_keywords').upsert(
                    records,
                    on_conflict='site_id,URL,Keyword,Month'
                ).execute()

                batch_saved = len(records)
                total_saved += batch_saved
                logger.info(f"Saved batch of {batch_saved} GSC keywords to Supabase ({total_saved}/{total_records})")
            except Exception as e:
                logger.error(f"Error saving GSC keywords batch: {e}")

        logger.info(f"Completed saving {total_saved} GSC keywords to Supabase")
        return total_saved
    
    def save_gsc_traffic(self, df: pd.DataFrame) -> Optional[Any]:
        """Save GSC traffic data to Supabase"""
        if df.empty or self.db_id is None:
            return None
        
        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id
        
        # Add traffic hash
        if 'traffic_hash' not in df.columns:
            df['traffic_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('URL', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )
        
        # Replace NaN values with None
        df = df.replace({float('nan'): None})
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        try:
            response = self.client.table('gsc_traffic').upsert(
                records,
                on_conflict='site_id,URL,Month'
            ).execute()
            
            logger.info(f"Saved {len(records)} GSC traffic records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GSC traffic: {e}")
            return None

    def save_internal_links(self, df: pd.DataFrame) -> Optional[Any]:
        """Save internal links data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')

        # Add link_hash if it doesn't exist (include anchor text to avoid duplicates)
        if 'link_hash' not in df.columns:
            df['link_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row['URL']}|{row['Target Hyperlink']}|{row.get('Anchor Text', '')}".encode()).hexdigest(),
                axis=1
            )

        # Filter to only include columns that exist in the database schema
        valid_columns = [
            'URL', 'Target Hyperlink', 'Anchor Text', 'Link Type',
            'URL Topic', 'Target Title', 'Relevance Score', 'Link Count',
            'link_hash', 'snapshot_date', 'site_id'
        ]
        df = df[[col for col in valid_columns if col in df.columns]]

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Handle duplicates by aggregating counts instead of removing
        original_count = len(df)
        if df.duplicated(subset=['link_hash']).any():
            logger.info(f"Found duplicate links, aggregating counts...")

            # Group by link_hash and aggregate
            df_grouped = df.groupby('link_hash').agg({
                'URL': 'first',
                'Target Hyperlink': 'first',
                'Anchor Text': 'first',
                'Link Type': 'first',
                'URL Topic': 'first',
                'Target Title': 'first',
                'Relevance Score': 'first',
                'site_id': 'first',
                'snapshot_date': 'first'
            }).reset_index()

            # Add count column
            link_counts = df.groupby('link_hash').size().reset_index(name='Link Count')
            df = df_grouped.merge(link_counts, on='link_hash')

            duplicates_removed = original_count - len(df)
            logger.info(f"Aggregated {original_count} links into {len(df)} unique links (combined {duplicates_removed} duplicates)")

            # Log high-frequency links (potential navigation or important links)
            high_frequency = df[df['Link Count'] > 3]
            if not high_frequency.empty:
                logger.info(f"Found {len(high_frequency)} high-frequency links (>3 occurrences):")
                for _, row in high_frequency.iterrows():
                    logger.info(f"  {row['Link Count']}x: {row['URL']} -> {row['Target Hyperlink']} ('{row.get('Anchor Text', '')}')")
        else:
            # No duplicates, add count column with value 1
            df['Link Count'] = 1
            logger.info(f"No duplicate links found, saving {len(df)} unique internal links")

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('internal_links').upsert(
                records,
                on_conflict='site_id,link_hash,snapshot_date'
            ).execute()

            logger.info(f"Saved {len(records)} internal links to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving internal links: {e}")
            return None

    def save_ga_data(self, df: pd.DataFrame) -> Optional[Any]:
        """Save Google Analytics data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id

        # Map column names from GA API to database schema
        column_mapping = {
            'pagePath': 'URL',
            'screenPageViews': 'Google Analytics Page Views',
            'activeUsers': 'Active Users'
        }

        # Apply column mapping
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})

        # Only keep columns that exist in the database schema
        valid_columns = ['URL', 'Google Analytics Page Views', 'Active Users', 'Month', 'site_id']
        df = df[[col for col in valid_columns if col in df.columns]]

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('ga_data').upsert(
                records,
                on_conflict='site_id,URL,Month'
            ).execute()

            logger.info(f"Saved {len(records)} GA records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GA data: {e}")
            return None

    def get_pages_data(self, date_filter: Optional[str] = None, limit: Optional[int] = None) -> pd.DataFrame:
        """Retrieve pages data from Supabase"""
        try:
            query = self.client.table('pages').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)
            if limit:
                query = query.limit(limit)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving pages data: {e}")
            return pd.DataFrame()

    def get_pages_data_for_excel(self, date_filter: Optional[str] = None, limit: Optional[int] = None) -> pd.DataFrame:
        """Retrieve pages data for Excel export (excludes internal database fields)"""
        try:
            # Select only user-relevant columns, exclude id, site_id, url_hash, snapshot_date
            columns = [
                'URL', 'SEO Title', 'Meta Description', 'H1', 'Page Content',
                'Focus Keyword', 'Page Type', 'Topic', 'Title Length',
                'GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views'
            ]

            query = self.client.table('pages').select(','.join(columns)).eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)
            if limit:
                query = query.limit(limit)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving pages data for Excel: {e}")
            return pd.DataFrame()

    def get_gsc_keywords(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC keywords data from Supabase"""
        try:
            query = self.client.table('gsc_keywords').select('*').eq('site_id', self.site_id)
            if date_filter:
                # For keywords, we might want to filter by month
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC keywords: {e}")
            return pd.DataFrame()

    def get_gsc_keywords_for_excel(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC keywords data for Excel export (excludes internal database fields)"""
        try:
            # Select only user-relevant columns, exclude id, site_id
            columns = ['URL', 'Keyword', 'Clicks', 'Impressions', 'CTR', 'Position', 'Month']

            query = self.client.table('gsc_keywords').select(','.join(columns)).eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC keywords for Excel: {e}")
            return pd.DataFrame()

    def get_gsc_traffic(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC traffic data from Supabase"""
        try:
            query = self.client.table('gsc_traffic').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC traffic: {e}")
            return pd.DataFrame()

    def get_gsc_traffic_for_excel(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC traffic data for Excel export (excludes internal database fields)"""
        try:
            # Select only user-relevant columns, exclude id, site_id
            columns = ['URL', 'Clicks', 'Impressions', 'CTR', 'Position', 'Month']

            query = self.client.table('gsc_traffic').select(','.join(columns)).eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC traffic for Excel: {e}")
            return pd.DataFrame()

    def get_internal_links(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve internal links data from Supabase"""
        try:
            query = self.client.table('internal_links').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving internal links: {e}")
            return pd.DataFrame()

    def get_internal_links_for_excel(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve internal links data for Excel export (excludes internal database fields)"""
        try:
            # Select only user-relevant columns, exclude id, site_id, link_hash, snapshot_date
            columns = [
                'URL', 'Target Hyperlink', 'Anchor Text', 'Link Type',
                'URL Topic', 'Target Title', 'Relevance Score', 'Link Count'
            ]

            query = self.client.table('internal_links').select(','.join(columns)).eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)

            response = query.execute()
            if response.data:
                df = pd.DataFrame(response.data)

                # Filter out external links (keep only Internal and Jump Link types)
                if 'Link Type' in df.columns and not df.empty:
                    internal_types = ['Internal', 'Jump Link', 'internal', 'jump link']
                    df = df[df['Link Type'].isin(internal_types)]
                    logger.info(f"Filtered internal links for Excel: {len(df)} internal links")

                return df
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving internal links for Excel: {e}")
            return pd.DataFrame()

    def get_ga_data(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve Google Analytics data from Supabase"""
        try:
            query = self.client.table('ga_data').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GA data: {e}")
            return pd.DataFrame()

    def get_ga_data_for_excel(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve Google Analytics data for Excel export (excludes internal database fields)"""
        try:
            # Select only user-relevant columns, exclude id, site_id
            columns = ['URL', 'Google Analytics Page Views', 'Active Users', 'Month']

            query = self.client.table('ga_data').select(','.join(columns)).eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GA data for Excel: {e}")
            return pd.DataFrame()

    # Optimized count methods for UI dashboard (avoid fetching large datasets)
    def get_data_counts(self) -> Dict[str, int]:
        """Get record counts for all data types efficiently"""
        try:
            counts = {}

            # Get pages count
            pages_response = self.client.table('pages').select('id', count='exact').eq('site_id', self.site_id).execute()
            counts['pages'] = pages_response.count or 0

            # Get keywords count
            keywords_response = self.client.table('gsc_keywords').select('id', count='exact').eq('site_id', self.site_id).execute()
            counts['keywords'] = keywords_response.count or 0

            # Get traffic count
            traffic_response = self.client.table('gsc_traffic').select('id', count='exact').eq('site_id', self.site_id).execute()
            counts['traffic_records'] = traffic_response.count or 0

            # Get internal links count
            links_response = self.client.table('internal_links').select('id', count='exact').eq('site_id', self.site_id).execute()
            counts['internal_links'] = links_response.count or 0

            # Get GA data count
            ga_response = self.client.table('ga_data').select('id', count='exact').eq('site_id', self.site_id).execute()
            counts['analytics_records'] = ga_response.count or 0

            return counts

        except Exception as e:
            logger.error(f"Error getting data counts: {e}")
            return {
                'pages': 0,
                'keywords': 0,
                'traffic_records': 0,
                'internal_links': 0,
                'analytics_records': 0
            }

    def get_available_dates_and_months(self) -> Dict[str, List[str]]:
        """Get available dates and months efficiently using DISTINCT queries"""
        try:
            available_dates = set()
            available_months = set()

            # Get unique snapshot dates from pages
            pages_dates = self.client.table('pages').select('snapshot_date').eq('site_id', self.site_id).execute()
            if pages_dates.data:
                for row in pages_dates.data:
                    if row.get('snapshot_date'):
                        available_dates.add(row['snapshot_date'])

            # Get unique snapshot dates from internal links
            links_dates = self.client.table('internal_links').select('snapshot_date').eq('site_id', self.site_id).execute()
            if links_dates.data:
                for row in links_dates.data:
                    if row.get('snapshot_date'):
                        available_dates.add(row['snapshot_date'])

            # Get unique months from keywords
            keywords_months = self.client.table('gsc_keywords').select('Month').eq('site_id', self.site_id).execute()
            if keywords_months.data:
                for row in keywords_months.data:
                    if row.get('Month'):
                        available_months.add(row['Month'])

            # Get unique months from traffic
            traffic_months = self.client.table('gsc_traffic').select('Month').eq('site_id', self.site_id).execute()
            if traffic_months.data:
                for row in traffic_months.data:
                    if row.get('Month'):
                        available_months.add(row['Month'])

            # Get unique months from GA data
            ga_months = self.client.table('ga_data').select('Month').eq('site_id', self.site_id).execute()
            if ga_months.data:
                for row in ga_months.data:
                    if row.get('Month'):
                        available_months.add(row['Month'])

            return {
                'available_dates': sorted(list(available_dates)),
                'available_months': sorted(list(available_months))
            }

        except Exception as e:
            logger.error(f"Error getting available dates and months: {e}")
            return {
                'available_dates': [],
                'available_months': []
            }

    def aggregate_gsc_data_by_url(self, keywords_df: pd.DataFrame) -> pd.DataFrame:
        """Aggregate GSC data by URL for the main Data sheet"""
        if keywords_df.empty:
            return pd.DataFrame(columns=['URL', 'GSC Clicks', 'GSC Impressions', 'CTR', 'Position'])

        df = keywords_df.copy()

        # Standardize column names from Supabase format
        column_mapping = {
            'page': 'URL',
            'query': 'Keyword',
            'clicks': 'Clicks',
            'impressions': 'Impressions',
            'ctr': 'CTR',
            'position': 'Position'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})

        # Aggregate by URL
        gsc_total_df = (
            df.groupby('URL', as_index=False)
              .agg({'Clicks': 'sum', 'Impressions': 'sum'})
        )

        # Calculate CTR
        gsc_total_df['CTR'] = gsc_total_df.apply(
            lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
            axis=1
        )

        # Calculate weighted average position
        pos_weight_all = (
            df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
              .groupby('URL', as_index=False)
              .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
        )
        pos_weight_all = pos_weight_all.rename(columns={'Impressions': 'IMP_sum'})

        gsc_total_df = gsc_total_df.merge(
            pos_weight_all[['URL', 'weighted_pos', 'IMP_sum']],
            on='URL',
            how='left'
        ).fillna({'weighted_pos': 0, 'IMP_sum': 1})

        gsc_total_df['Position'] = gsc_total_df.apply(
            lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
            axis=1
        )

        # Clean up and rename columns to match original format
        gsc_total_df = gsc_total_df.drop(columns=['weighted_pos', 'IMP_sum'])
        gsc_total_df = gsc_total_df.rename(columns={
            'Clicks': 'GSC Clicks',
            'Impressions': 'GSC Impressions'
        })

        return gsc_total_df

    def aggregate_ga_data_by_url(self, ga_df: pd.DataFrame) -> pd.DataFrame:
        """Aggregate GA data by URL for the main Data sheet"""
        if ga_df.empty:
            return pd.DataFrame(columns=['URL', 'Google Analytics Page Views', 'Active Users'])

        df = ga_df.copy()

        # Standardize column names from Supabase format
        column_mapping = {
            'page_path': 'pagePath',
            'page_views': 'screenPageViews',
            'active_users': 'activeUsers'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})

        # Handle different possible path column names
        path_column = None
        for col in ['pagePath', 'page_path', 'URL']:
            if col in df.columns:
                path_column = col
                break

        if not path_column:
            logger.warning("No valid path column found in GA data")
            return pd.DataFrame(columns=['URL', 'Google Analytics Page Views', 'Active Users'])

        # If we have a path column, build full URLs
        if path_column in ['pagePath', 'page_path']:
            # Keep only valid paths (those starting with "/")
            df = df[df[path_column].str.startswith('/')].copy()

            # Build the full-URL column
            homepage = f"https://{self.domain}"
            df['URL'] = homepage + df[path_column]

        # Cast metrics to int BEFORE grouping
        if 'screenPageViews' in df.columns:
            df['screenPageViews'] = df['screenPageViews'].astype(int)
        if 'activeUsers' in df.columns:
            df['activeUsers'] = df['activeUsers'].astype(int)
        if 'page_views' in df.columns:
            df['page_views'] = df['page_views'].astype(int)
        if 'active_users' in df.columns:
            df['active_users'] = df['active_users'].astype(int)

        # Aggregate per-URL for the "Data" sheet
        agg_columns = {}
        if 'screenPageViews' in df.columns:
            agg_columns['screenPageViews'] = 'sum'
        elif 'page_views' in df.columns:
            agg_columns['page_views'] = 'sum'

        if 'activeUsers' in df.columns:
            agg_columns['activeUsers'] = 'sum'
        elif 'active_users' in df.columns:
            agg_columns['active_users'] = 'sum'

        if not agg_columns:
            logger.warning("No valid metrics found in GA data. Available columns: " + str(list(df.columns)))
            return pd.DataFrame(columns=['URL', 'Google Analytics Page Views', 'Active Users'])

        ga_total_df = (
            df.groupby('URL', as_index=False)
             .agg(agg_columns)
        )

        # Rename columns to match original format
        rename_mapping = {
            'screenPageViews': 'Google Analytics Page Views',
            'page_views': 'Google Analytics Page Views',
            'activeUsers': 'Active Users',
            'active_users': 'Active Users'
        }

        for old_col, new_col in rename_mapping.items():
            if old_col in ga_total_df.columns:
                ga_total_df = ga_total_df.rename(columns={old_col: new_col})

        return ga_total_df

    def create_historical_traffic_sheet(self, keywords_df: pd.DataFrame) -> pd.DataFrame:
        """Create Historical-Traffic sheet by aggregating GSC data by URL and Month"""
        if keywords_df.empty:
            return pd.DataFrame(columns=['URL', 'Month', 'Clicks', 'Impressions', 'CTR', 'Position'])

        df = keywords_df.copy()

        # Standardize column names
        column_mapping = {
            'page': 'URL',
            'clicks': 'Clicks',
            'impressions': 'Impressions',
            'position': 'Position'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})

        # Aggregate by URL and Month
        hist_traffic_df = (
            df.groupby(['URL', 'Month'], as_index=False)
             .agg({'Clicks': 'sum', 'Impressions': 'sum'})
        )

        # Calculate CTR
        hist_traffic_df['CTR'] = hist_traffic_df.apply(
            lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
            axis=1
        )

        # Calculate weighted average position
        pos_weight = (
            df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
             .groupby(['URL', 'Month'], as_index=False)
             .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
        )
        pos_weight = pos_weight.rename(columns={'Impressions': 'IMP_sum'})

        hist_traffic_df = hist_traffic_df.merge(
            pos_weight[['URL', 'Month', 'weighted_pos', 'IMP_sum']],
            on=['URL', 'Month'],
            how='left'
        ).fillna({'weighted_pos': 0, 'IMP_sum': 1})

        hist_traffic_df['Position'] = hist_traffic_df.apply(
            lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
            axis=1
        )

        hist_traffic_df = hist_traffic_df.drop(columns=['weighted_pos', 'IMP_sum'])

        return hist_traffic_df

    def create_comprehensive_data_sheet(self, pages_df: pd.DataFrame,
                                      keywords_df: pd.DataFrame, ga_df: pd.DataFrame) -> pd.DataFrame:
        """Create the comprehensive Data sheet that merges all data sources"""

        # Prepare crawl data
        crawl_df = pages_df.copy() if not pages_df.empty else pd.DataFrame()

        if not crawl_df.empty:
            # Standardize column names from Supabase format
            column_mapping = {
                'url': 'URL',
                'title': 'SEO Title',
                'description': 'Meta Description',
                'h1': 'H1',
                'text': 'Page Content'
            }

            for old_col, new_col in column_mapping.items():
                if old_col in crawl_df.columns and new_col not in crawl_df.columns:
                    crawl_df = crawl_df.rename(columns={old_col: new_col})

            # Add calculated fields
            if 'SEO Title' in crawl_df.columns:
                crawl_df['Title Length'] = crawl_df['SEO Title'].apply(lambda x: len(x) if isinstance(x, str) else 0)

            # Add placeholder columns
            crawl_df['Focus Keyword'] = ''
            crawl_df['Page Type'] = ''
            crawl_df['Topic'] = ''
        else:
            crawl_df = pd.DataFrame(columns=[
                'URL', 'Page Content', 'SEO Title', 'Title Length',
                'Meta Description', 'H1', 'Focus Keyword', 'Page Type', 'Topic'
            ])

        # Aggregate GSC data by URL
        gsc_total_df = self.aggregate_gsc_data_by_url(keywords_df)

        # Aggregate GA data by URL
        ga_total_df = self.aggregate_ga_data_by_url(ga_df)

        # Merge all data sources
        data_df = crawl_df.merge(gsc_total_df, on='URL', how='left')
        data_df = data_df.merge(ga_total_df, on='URL', how='left')

        # Fill NaNs with 0 for numeric metrics
        numeric_columns = ['GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views', 'Active Users']
        for col in numeric_columns:
            if col in data_df.columns:
                data_df[col] = data_df[col].fillna(0).infer_objects(copy=False)
                data_df[col] = data_df[col].astype(int)

        # Reorder columns to match original format
        data_columns = [
            'URL',
            'GSC Clicks',
            'GSC Impressions',
            'Google Analytics Page Views',
            'Focus Keyword',
            'Page Type',
            'Topic',
            'Page Content',
            'SEO Title',
            'Title Length',
            'Meta Description',
            'H1'
        ]

        # Only include columns that exist
        data_df = data_df[[col for col in data_columns if col in data_df.columns]]

        return data_df

    def generate_excel_report(self, output_dir: str, date_filter: Optional[str] = None,
                            include_raw_data: bool = True, include_keywords: bool = True,
                            include_traffic: bool = True, include_links: bool = True,
                            include_analytics: bool = True) -> str:
        """Generate Excel report from Supabase data - matches original main.py format"""
        import os

        logger.info(f"Generating Excel report from Supabase data for domain: {self.domain}")

        # Create unique Excel file path to prevent conflicts
        from src.utils.file_utils import get_unique_excel_filename
        filename = get_unique_excel_filename(self.domain, "supabase_report", date_filter)
        excel_path = os.path.join(output_dir, filename)

        # Use xlsxwriter engine to match original format
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:

            # Get all data first using clean Excel methods (excludes internal database fields)
            pages_df = self.get_pages_data_for_excel(date_filter) if include_raw_data else pd.DataFrame()
            keywords_df = self.get_gsc_keywords_for_excel(date_filter) if include_keywords else pd.DataFrame()
            ga_df = self.get_ga_data_for_excel(date_filter) if include_analytics else pd.DataFrame()
            links_df = self.get_internal_links_for_excel(date_filter) if include_links else pd.DataFrame()

            # 1. Data sheet - comprehensive merged data (main sheet)
            if include_raw_data:
                data_df = self.create_comprehensive_data_sheet(pages_df, keywords_df, ga_df)
                data_df.to_excel(writer, sheet_name='Data', index=False)
                logger.info(f"Added comprehensive Data sheet with {len(data_df)} pages")

            # 2. Keywords sheet - raw GSC keyword data
            if include_keywords:
                if not keywords_df.empty:
                    keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                    logger.info(f"Added {len(keywords_df)} keywords to Excel report")
                else:
                    # Create empty sheet with headers
                    empty_df = pd.DataFrame(columns=['page', 'query', 'clicks', 'impressions', 'ctr', 'position', 'Month'])
                    empty_df.to_excel(writer, sheet_name='Keywords', index=False)
                    logger.info("Added empty Keywords sheet (no keywords data)")

            # 3. Historical-Traffic sheet - aggregated traffic by URL and month
            if include_traffic:
                hist_traffic_df = self.create_historical_traffic_sheet(keywords_df)
                hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
                logger.info(f"Added Historical-Traffic sheet with {len(hist_traffic_df)} records")

            # 4. Internal-Links sheet
            if include_links:
                if not links_df.empty:
                    # Data is already clean (internal links only, no database fields)
                    links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
                    logger.info(f"Added {len(links_df)} internal links to Excel report")
                else:
                    # Create empty sheet with headers (matching clean structure)
                    empty_df = pd.DataFrame(columns=[
                        'URL', 'Target Hyperlink', 'Anchor Text', 'Link Type',
                        'URL Topic', 'Target Title', 'Relevance Score', 'Link Count'
                    ])
                    empty_df.to_excel(writer, sheet_name='Internal-Links', index=False)
                    logger.info("Added empty Internal-Links sheet (no links data)")

            # 5. GA-Data sheet - Google Analytics data
            if include_analytics:
                if not ga_df.empty:
                    ga_df.to_excel(writer, sheet_name='GA-Data', index=False)
                    logger.info(f"Added {len(ga_df)} GA records to Excel report")
                else:
                    # Create empty sheet with headers
                    empty_df = pd.DataFrame(columns=[
                        'pagePath', 'sessions', 'screenPageViews', 'bounceRate',
                        'averageSessionDuration', 'Month'
                    ])
                    empty_df.to_excel(writer, sheet_name='GA-Data', index=False)
                    logger.info("Added empty GA-Data sheet (no analytics data)")

        logger.info(f"Excel report generated from Supabase data: {excel_path}")
        return excel_path

    def prepare_internal_links_for_excel(self, links_df: pd.DataFrame) -> pd.DataFrame:
        """Filter internal links for Excel export - remove external links and ID columns"""
        if links_df.empty:
            return links_df

        # Make a copy to avoid modifying original
        filtered_df = links_df.copy()

        # Filter out external links (keep only Internal and Jump Link types)
        if 'Link Type' in filtered_df.columns:
            # Keep only Internal and Jump Link types, exclude External
            internal_types = ['Internal', 'Jump Link', 'internal', 'jump link']
            filtered_df = filtered_df[filtered_df['Link Type'].isin(internal_types)]
            logger.info(f"Filtered out external links: {len(links_df)} -> {len(filtered_df)} internal links")

        # Remove ID and site_id columns for cleaner Excel output
        columns_to_remove = ['id', 'site_id']
        for col in columns_to_remove:
            if col in filtered_df.columns:
                filtered_df = filtered_df.drop(columns=[col])

        # Reorder columns for better Excel presentation
        preferred_order = [
            'URL', 'Target Hyperlink', 'Anchor Text', 'Link Type',
            'URL Topic', 'Target Title', 'Relevance Score', 'Link Count',
            'link_hash', 'snapshot_date'
        ]

        # Only include columns that exist in the dataframe
        available_columns = [col for col in preferred_order if col in filtered_df.columns]
        remaining_columns = [col for col in filtered_df.columns if col not in available_columns]
        final_columns = available_columns + remaining_columns

        filtered_df = filtered_df[final_columns]

        return filtered_df
