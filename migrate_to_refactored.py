#!/usr/bin/env python3
"""
Migration script to help transition from the old monolithic structure to the new refactored structure
"""
import os
import shutil
import sys
from pathlib import Path


def backup_old_files():
    """Backup the original files"""
    backup_dir = "backup_original"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = ["main.py", "api.py"]
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(backup_dir, file))
            print(f"✅ Backed up {file} to {backup_dir}/")
    
    print(f"📁 Original files backed up to {backup_dir}/")


def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        ("pydantic", "pydantic"),
        ("pydantic-settings", "pydantic_settings"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pandas", "pandas"),
        ("beautifulsoup4", "bs4"),
        ("requests", "requests"),
        ("playwright", "playwright"),
        ("google-api-python-client", "googleapiclient"),
        ("python-dateutil", "dateutil"),
        ("markdownify", "markdownify"),
        ("python-dotenv", "dotenv")
    ]
    
    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name} - MISSING")
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed!")
    return True


def test_refactored_structure():
    """Test if the refactored structure works"""
    print("🧪 Testing refactored structure...")
    
    try:
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Test imports
        from src.config.settings import settings
        from src.api.app import app
        from src.services.analysis_service import SEOAnalysisService
        
        print("✅ Core imports successful")
        
        # Test settings
        print(f"✅ Settings loaded - API will run on {settings.api_host}:{settings.api_port}")
        
        # Test service initialization
        service = SEOAnalysisService()
        print("✅ Analysis service initialized")
        
        print("✅ Refactored structure is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing refactored structure: {e}")
        return False


def create_env_file():
    """Create .env file from .env.example if it doesn't exist"""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy2(".env.example", ".env")
            print("✅ Created .env file from .env.example")
        else:
            print("⚠️  .env.example not found, you may need to create .env manually")
    else:
        print("✅ .env file already exists")


def show_migration_summary():
    """Show summary of the migration"""
    print("\n" + "="*60)
    print("🎉 MIGRATION COMPLETE!")
    print("="*60)
    
    print("\n📁 NEW PROJECT STRUCTURE:")
    print("├── src/                    # Refactored modular code")
    print("├── public/                 # Web dashboard files")
    print("├── main_refactored.py      # New CLI entry point")
    print("├── api_refactored.py       # New API entry point")
    print("├── requirements.txt        # Updated dependencies")
    print("├── .env                    # Environment configuration")
    print("└── backup_original/        # Your original files")
    
    print("\n🚀 HOW TO RUN:")
    print("1. API Server:    python main_refactored.py serve")
    print("2. CLI Analysis:  python main_refactored.py analyze config.json")
    print("3. Direct API:    python api_refactored.py")
    
    print("\n🌐 ACCESS:")
    print("- Dashboard: http://localhost:8000")
    print("- API Docs:  http://localhost:8000/docs")
    
    print("\n📚 BENEFITS OF REFACTORED VERSION:")
    print("✅ Modular architecture - easier to maintain")
    print("✅ Better error handling and logging")
    print("✅ Environment-based configuration")
    print("✅ Type hints and data validation")
    print("✅ Easier testing and debugging")
    print("✅ Scalable structure for future features")
    
    print("\n⚠️  IMPORTANT NOTES:")
    print("- Your original files are safely backed up")
    print("- Update any scripts that reference main.py or api.py")
    print("- Configure .env file for your environment")
    print("- The web interface remains the same")


def main():
    """Main migration function"""
    print("🔄 SEO Analysis Tool - Migration to Refactored Structure")
    print("="*60)
    
    # Step 1: Backup original files
    backup_old_files()
    
    # Step 2: Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies before continuing")
        return
    
    # Step 3: Create environment file
    create_env_file()
    
    # Step 4: Test refactored structure
    if not test_refactored_structure():
        print("\n❌ Refactored structure test failed")
        return
    
    # Step 5: Show summary
    show_migration_summary()


if __name__ == "__main__":
    main()
