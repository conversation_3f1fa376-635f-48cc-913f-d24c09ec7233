-- Database Migration: Add Configuration Columns to Sites Table
-- Run this in your Supabase SQL Editor

-- Add configuration columns to sites table
ALTER TABLE sites 
ADD COLUMN IF NOT EXISTS domain_property TEXT,
ADD COLUMN IF NOT EXISTS ga_property_id TEXT,
ADD COLUMN IF NOT EXISTS service_account_data JSONB,
ADD COLUMN IF NOT EXISTS homepage TEXT,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();

-- Update existing sites to have a last_updated timestamp
UPDATE sites 
SET last_updated = created_at 
WHERE last_updated IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sites_domain_property ON sites(domain_property);
CREATE INDEX IF NOT EXISTS idx_sites_ga_property_id ON sites(ga_property_id);

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'sites' 
ORDER BY ordinal_position;
