"""
Web crawling functionality
"""
import asyncio
import requests
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright

from src.config.settings import settings
from src.models.schemas import <PERSON>raw<PERSON><PERSON><PERSON>ult
from src.utils.text_processing import html_to_markdown
from src.utils.logging import get_logger

logger = get_logger(__name__)


class WebCrawler:
    """Web crawler for extracting content from websites"""
    
    def __init__(self, timeout: int = None):
        self.timeout = timeout or settings.crawl_timeout
        
    async def get_js_rendered_html(self, url: str) -> str:
        """Get HTML content using JavaScript rendering with <PERSON><PERSON>"""
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            await page.goto(url, timeout=settings.js_render_timeout)
            content = await page.content()
            await browser.close()
            return content
    
    def extract_internal_links(self, base_url: str, html: str) -> List[str]:
        """Extract all internal links from HTML content"""
        soup = BeautifulSoup(html, 'html.parser')
        domain = urlparse(base_url).netloc
        links = set()
        
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            full_url = urljoin(base_url, href)
            parsed_url = urlparse(full_url)
            
            # Only include URLs from the same domain and exclude fragments
            if parsed_url.netloc == domain:
                # Remove fragments (#) from URLs
                clean_url = full_url.split('#')[0]
                # Remove trailing slash for consistency
                if clean_url.endswith('/') and clean_url != base_url + '/':
                    clean_url = clean_url[:-1]
                links.add(clean_url)
        
        return list(links)
    
    def crawl_url(self, url: str) -> Optional[CrawlResult]:
        """Crawl a single URL and extract content"""
        try:
            response = requests.get(url, timeout=self.timeout)
            if response.status_code != 200:
                logger.warning(f"Skipping dead link: {url} (Status code: {response.status_code})")
                return None
            
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            title = soup.title.string if soup.title else 'No title'
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            h1 = soup.find('h1')
            
            return CrawlResult(
                url=url,
                title=title,
                description=meta_desc['content'] if meta_desc else '',
                h1=h1.text if h1 else '',
                text=html_to_markdown(html_content),
                raw_html=html_content
            )
        except Exception as e:
            logger.error(f"Error crawling {url}: {e}")
            return None
    
    async def crawl_url_with_js(self, url: str) -> Optional[CrawlResult]:
        """Crawl a URL using JavaScript rendering as fallback"""
        try:
            logger.info(f"Trying JS rendering for {url}...")
            html = await self.get_js_rendered_html(url)
            soup = BeautifulSoup(html, 'html.parser')
            
            text = html_to_markdown(html)
            
            return CrawlResult(
                url=url,
                title=soup.title.string if soup.title else 'No title',
                description=soup.find('meta', attrs={'name': 'description'})['content'] 
                           if soup.find('meta', attrs={'name': 'description'}) else '',
                h1=soup.find('h1').text if soup.find('h1') else '',
                text=text,
                raw_html=html
            )
        except Exception as e:
            logger.error(f"JS rendering failed for {url}: {e}")
            return None
    
    async def crawl_site(self, urls: List[str], output_dir: str) -> List[CrawlResult]:
        """Crawl multiple URLs and return results"""
        results = []
        failed_urls = []

        for url in urls:
            data = self.crawl_url(url)
            if not data:
                # Try JavaScript rendering as fallback
                data = await self.crawl_url_with_js(url)
                if not data:
                    failed_urls.append(url)
                    continue

            results.append(data)

        # Save failed URLs for debugging
        if failed_urls:
            import os
            with open(os.path.join(output_dir, 'failed_urls.txt'), 'w') as f:
                for url in failed_urls:
                    f.write(url + '\n')

        return results
    
    async def discover_urls_from_homepage(self, homepage: str) -> List[str]:
        """Discover all internal URLs from a homepage using JavaScript rendering"""
        logger.info(f"Discovering URLs from homepage {homepage}...")
        try:
            # Use JavaScript rendering to get the full HTML
            html = await self.get_js_rendered_html(homepage)
            
            # Extract all internal links
            links = self.extract_internal_links(homepage, html)
            
            # Add the homepage itself and remove duplicates
            all_urls = [homepage] + links
            unique_urls = list(dict.fromkeys(all_urls))  # Remove duplicates while preserving order
            
            logger.info(f"Discovered {len(unique_urls)} URLs")
            return unique_urls
        except Exception as e:
            logger.error(f"Failed to discover URLs from homepage {homepage}: {e}")
            return [homepage]
