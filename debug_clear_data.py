#!/usr/bin/env python3
"""
Debug the clear site data issue
"""
import os
from datetime import datetime

# Get environment variables
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

if not SUPABASE_URL or not SUPABASE_KEY:
    print("❌ SUPABASE_URL and SUPABASE_KEY environment variables must be set")
    print("Please set them in your environment or .env file")
    exit(1)

try:
    from supabase import create_client
    client = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Get all sites
    print("🔍 Fetching all sites...")
    sites_response = client.table('sites').select('*').execute()
    
    if not sites_response.data:
        print("❌ No sites found")
        exit(1)
    
    print(f"📊 Found {len(sites_response.data)} sites:")
    
    # Find a site with configuration
    test_site = None
    for site in sites_response.data:
        print(f"\n  Site ID: {site['id']} ({type(site['id'])})")
        print(f"  Domain: {site['domain']}")
        print(f"  domain_property: {site.get('domain_property')}")
        print(f"  ga_property_id: {site.get('ga_property_id')}")
        print(f"  has service_account_data: {bool(site.get('service_account_data'))}")
        print(f"  homepage: {site.get('homepage')}")
        print(f"  wp_api_key: {site.get('wp_api_key')}")
        
        if site.get('domain_property') and site.get('ga_property_id'):
            test_site = site
    
    if not test_site:
        print("\n❌ No sites with configuration found")
        exit(1)
    
    site_id = test_site['id']
    print(f"\n🧪 Testing with site ID: {site_id} (type: {type(site_id)})")
    
    # Store original values
    original_domain_property = test_site.get('domain_property')
    original_ga_property_id = test_site.get('ga_property_id')
    original_service_account_data = test_site.get('service_account_data')
    
    print(f"\n📋 Original configuration:")
    print(f"  - domain_property: {original_domain_property}")
    print(f"  - ga_property_id: {original_ga_property_id}")
    print(f"  - has service_account_data: {bool(original_service_account_data)}")
    
    # Test update operation (what the clear data function does)
    print(f"\n🔄 Testing update operation (only updating last_updated)...")
    
    update_response = client.table('sites').update({
        'last_updated': datetime.now().isoformat()
    }).eq('id', site_id).execute()
    
    print(f"Update response data: {update_response.data}")
    
    # Verify the site after update
    print(f"\n🔍 Verifying site after update...")
    verify_response = client.table('sites').select('*').eq('id', site_id).execute()
    
    if verify_response.data:
        updated_site = verify_response.data[0]
        print(f"\n📋 Configuration after update:")
        print(f"  - domain_property: {updated_site.get('domain_property')}")
        print(f"  - ga_property_id: {updated_site.get('ga_property_id')}")
        print(f"  - has service_account_data: {bool(updated_site.get('service_account_data'))}")
        print(f"  - homepage: {updated_site.get('homepage')}")
        print(f"  - wp_api_key: {updated_site.get('wp_api_key')}")
        
        # Compare values
        domain_property_preserved = updated_site.get('domain_property') == original_domain_property
        ga_property_id_preserved = updated_site.get('ga_property_id') == original_ga_property_id
        service_account_preserved = bool(updated_site.get('service_account_data')) == bool(original_service_account_data)
        
        print(f"\n🔍 Comparison:")
        print(f"  - domain_property preserved: {domain_property_preserved}")
        print(f"  - ga_property_id preserved: {ga_property_id_preserved}")
        print(f"  - service_account_data preserved: {service_account_preserved}")
        
        if domain_property_preserved and ga_property_id_preserved and service_account_preserved:
            print(f"\n✅ ALL CONFIGURATION PRESERVED! The issue might be elsewhere.")
        else:
            print(f"\n❌ CONFIGURATION NOT PRESERVED!")
            print(f"   This indicates a database issue (triggers, constraints, or RLS policies)")
            
            if not domain_property_preserved:
                print(f"   domain_property: '{original_domain_property}' -> '{updated_site.get('domain_property')}'")
            if not ga_property_id_preserved:
                print(f"   ga_property_id: '{original_ga_property_id}' -> '{updated_site.get('ga_property_id')}'")
            if not service_account_preserved:
                print(f"   service_account_data: {bool(original_service_account_data)} -> {bool(updated_site.get('service_account_data'))}")
    else:
        print(f"\n❌ Could not verify site after update - site not found!")
    
    # Test data deletion simulation
    print(f"\n🗑️ Testing data deletion simulation...")
    tables_to_check = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
    
    for table in tables_to_check:
        try:
            count_response = client.table(table).select('id').eq('site_id', site_id).execute()
            count = len(count_response.data) if count_response.data else 0
            print(f"  - {table}: {count} records for site_id {site_id}")
        except Exception as e:
            print(f"  - {table}: Error - {e}")
    
    print(f"\n✅ Debug test completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
