"""
Test concurrency and multi-user safety of the API
"""
import asyncio
import aiohttp
import pytest
import uuid
import os
import tempfile
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import threading
import time

# Test configuration
API_BASE_URL = "http://localhost:8000"
TEST_DOMAIN = "example.com"


class TestConcurrency:
    """Test concurrent API usage"""
    
    def test_task_progress_thread_safety(self):
        """Test that task progress storage is thread-safe"""
        from src.api.routes import task_progress, update_task_progress
        
        # Clear any existing progress
        task_progress._progress.clear()
        
        def worker(worker_id: int):
            """Worker function that updates task progress"""
            for i in range(10):
                task_id = f"task_{worker_id}_{i}"
                update_task_progress(task_id, i * 10, f"Worker {worker_id} progress {i}")
                time.sleep(0.01)  # Small delay to increase chance of race conditions
        
        # Run multiple workers concurrently
        threads = []
        for worker_id in range(5):
            thread = threading.Thread(target=worker, args=(worker_id,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all tasks were recorded correctly
        expected_tasks = 5 * 10  # 5 workers * 10 tasks each
        actual_tasks = len(task_progress._progress)
        assert actual_tasks == expected_tasks, f"Expected {expected_tasks} tasks, got {actual_tasks}"
        
        # Verify no data corruption
        for worker_id in range(5):
            for i in range(10):
                task_id = f"task_{worker_id}_{i}"
                task_data = task_progress.get(task_id)
                assert task_data is not None, f"Task {task_id} not found"
                assert task_data['progress'] == i * 10, f"Wrong progress for {task_id}"
                assert f"Worker {worker_id}" in task_data['message'], f"Wrong message for {task_id}"
    
    def test_unique_file_generation(self):
        """Test that file generation creates unique files"""
        from src.utils.file_utils import get_unique_excel_filename, get_output_directory
        
        # Generate multiple filenames concurrently
        filenames = []
        
        def generate_filename():
            filename = get_unique_excel_filename(TEST_DOMAIN, "test_report")
            filenames.append(filename)
        
        # Run multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=generate_filename)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Verify all filenames are unique
        assert len(filenames) == len(set(filenames)), "Generated filenames are not unique"
        
        # Verify all filenames contain expected components
        for filename in filenames:
            assert TEST_DOMAIN.replace('.', '_') in filename
            assert 'test_report' in filename
            assert filename.endswith('.xlsx')
    
    def test_unique_output_directories(self):
        """Test that output directories are unique per session"""
        from src.utils.file_utils import get_output_directory
        
        # Generate multiple output directories with different session IDs
        directories = []
        session_ids = [str(uuid.uuid4()) for _ in range(5)]
        
        for session_id in session_ids:
            output_dir = get_output_directory(f"https://{TEST_DOMAIN}", session_id=session_id)
            directories.append(output_dir)
        
        # Verify all directories are unique
        assert len(directories) == len(set(directories)), "Generated directories are not unique"
        
        # Verify directories contain session IDs
        for i, directory in enumerate(directories):
            assert session_ids[i] in directory, f"Session ID {session_ids[i]} not in directory {directory}"
    
    def test_temp_file_uniqueness(self):
        """Test that temporary files are unique"""
        from src.utils.file_utils import create_temp_file
        
        # Generate multiple temp files concurrently
        temp_files = []
        
        def create_file():
            temp_file = create_temp_file("test content", ".json")
            temp_files.append(temp_file)
        
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=create_file)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Verify all temp files are unique
        assert len(temp_files) == len(set(temp_files)), "Generated temp files are not unique"
        
        # Verify all files exist and contain expected content
        for temp_file in temp_files:
            assert os.path.exists(temp_file), f"Temp file {temp_file} does not exist"
            with open(temp_file, 'r') as f:
                content = f.read()
                assert content == "test content", f"Wrong content in {temp_file}"
        
        # Cleanup
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
            except:
                pass
    
    def test_download_endpoint_security(self):
        """Test download endpoint security against path traversal"""
        from src.api.routes import router
        from fastapi.testclient import TestClient
        from src.api.app import create_app
        
        app = create_app()
        client = TestClient(app)
        
        # Test various path traversal attempts
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/passwd",
            "C:\\windows\\system32\\config\\sam",
            "../../src/config/settings.py",
            "../api.py"
        ]
        
        for path in malicious_paths:
            response = client.get(f"/download/{path}")
            # Should return 400 (bad request) or 403 (forbidden), not 200
            assert response.status_code in [400, 403, 404], f"Path traversal not blocked for: {path}"
    
    @pytest.mark.asyncio
    async def test_concurrent_api_requests(self):
        """Test multiple concurrent API requests don't interfere with each other"""
        # This test requires the API server to be running
        # Skip if server is not available
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{API_BASE_URL}/health") as response:
                    if response.status != 200:
                        pytest.skip("API server not available")
        except:
            pytest.skip("API server not available")
        
        async def make_request(session, endpoint):
            """Make a request and return the response"""
            try:
                async with session.get(f"{API_BASE_URL}{endpoint}") as response:
                    return await response.json()
            except Exception as e:
                return {"error": str(e)}
        
        # Make multiple concurrent requests
        async with aiohttp.ClientSession() as session:
            tasks = []
            for i in range(10):
                task = make_request(session, "/health")
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            
            # Verify all requests succeeded
            for result in results:
                assert "error" not in result, f"Request failed: {result}"
                assert result.get("status") == "healthy", f"Unexpected response: {result}"


if __name__ == "__main__":
    # Run basic tests
    test = TestConcurrency()
    
    print("Testing task progress thread safety...")
    test.test_task_progress_thread_safety()
    print("✓ Task progress thread safety test passed")
    
    print("Testing unique file generation...")
    test.test_unique_file_generation()
    print("✓ Unique file generation test passed")
    
    print("Testing unique output directories...")
    test.test_unique_output_directories()
    print("✓ Unique output directories test passed")
    
    print("Testing temp file uniqueness...")
    test.test_temp_file_uniqueness()
    print("✓ Temp file uniqueness test passed")
    
    print("Testing download endpoint security...")
    test.test_download_endpoint_security()
    print("✓ Download endpoint security test passed")
    
    print("\nAll concurrency tests passed! ✓")
