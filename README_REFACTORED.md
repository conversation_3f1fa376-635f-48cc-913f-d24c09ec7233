# SEO Analysis Tool - Refactored

A comprehensive SEO analysis tool with web crawling, Google Search Console/Analytics integration, and automated reporting capabilities. This version has been refactored following Python best practices for better maintainability, testability, and scalability.

## 🏗️ Architecture

The application follows a modular architecture with clear separation of concerns:

```
src/
├── __init__.py
├── config/                 # Configuration management
│   ├── __init__.py
│   └── settings.py        # Pydantic settings with env support
├── models/                # Data models and schemas
│   ├── __init__.py
│   └── schemas.py         # Pydantic models for API/data
├── core/                  # Core business logic
│   ├── __init__.py
│   ├── crawler.py         # Web crawling functionality
│   ├── google_apis.py     # GSC/GA API clients
│   └── wordpress.py       # WordPress API integration
├── database/              # Database integrations
│   ├── __init__.py
│   └── supabase_client.py # Supabase client
├── services/              # High-level business services
│   ├── __init__.py
│   ├── analysis_service.py    # Main analysis orchestration
│   ├── link_analysis_service.py # Internal link analysis
│   └── report_service.py      # Report generation
├── utils/                 # Utility functions
│   ├── __init__.py
│   ├── logging.py         # Logging configuration
│   ├── text_processing.py # Text/HTML processing
│   └── file_utils.py      # File handling utilities
├── api/                   # FastAPI application
│   ├── __init__.py
│   ├── app.py            # FastAPI app setup
│   └── routes.py         # API route definitions
└── cli/                   # Command-line interface
    ├── __init__.py
    └── main.py           # CLI entry point
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

Copy the example environment file and customize:

```bash
cp .env.example .env
# Edit .env with your settings
```

### 3. Run the Application

**Option A: API Server (Recommended)**
```bash
python main_refactored.py serve
# OR
python api_refactored.py
```

**Option B: CLI Analysis**
```bash
python main_refactored.py analyze config.json
```

### 4. Access the Dashboard

Open your browser to: http://localhost:8000

## 📋 Features

### ✅ Web Crawling
- JavaScript-rendered content support (Playwright)
- Configurable timeouts and concurrency
- Automatic URL discovery from homepage
- Failed URL tracking and retry logic

### ✅ Google Integrations
- **Search Console**: Keywords, traffic, performance data
- **Analytics 4**: Sessions, pageviews, bounce rates
- Monthly data aggregation
- Service account authentication

### ✅ WordPress Integration
- WordPress Data Exporter plugin support
- Automatic API endpoint detection
- Internal links data extraction

### ✅ Database Storage
- **Supabase** integration for data persistence
- Automatic schema management
- Conflict resolution (upserts)
- Historical data tracking

### ✅ Report Generation
- **Excel reports** with multiple sheets
- **CSV exports** for individual datasets
- **Summary reports** with key metrics
- Downloadable via web interface

### ✅ Web Interface
- Modern Bootstrap-based dashboard
- Real-time progress tracking
- File upload for service accounts
- Configuration management

## 🔧 Configuration

### Environment Variables

The application uses environment variables for configuration. See `.env.example` for all available options.

Key settings:
- `API_HOST` / `API_PORT`: Server binding
- `DEBUG`: Enable debug mode
- `REPORTS_DIR`: Output directory for reports
- `SUPABASE_URL` / `SUPABASE_KEY`: Database connection (automatically used when available)

### Configuration File Format

For CLI usage, create a JSON configuration file:

```json
{
  "domain_property": "https://example.com/",
  "ga_property_id": "*********",
  "service_account_file": "path/to/service-account.json",
  "homepage": "https://example.com/",
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "website_urls": [
    "https://example.com/page1",
    "https://example.com/page2"
  ],
  "wp_api_key": "your-wordpress-api-key"
}
```

**Note**: Supabase credentials (`supabase_url` and `supabase_key`) are automatically loaded from environment variables if not provided in the configuration file. This is the recommended approach for security.

## 🛠️ Development

### Project Structure Benefits

1. **Modularity**: Each component has a single responsibility
2. **Testability**: Easy to unit test individual components
3. **Maintainability**: Clear separation makes changes easier
4. **Scalability**: Can easily add new features or integrations
5. **Configuration**: Environment-based configuration management
6. **Logging**: Centralized logging with configurable levels

### Key Improvements

- **Dependency Injection**: Services are loosely coupled
- **Error Handling**: Comprehensive error handling and logging
- **Type Hints**: Full type annotations for better IDE support
- **Async Support**: Proper async/await patterns
- **Configuration Management**: Pydantic-based settings
- **Data Validation**: Pydantic models for all data structures

### Adding New Features

1. **New API Endpoint**: Add to `src/api/routes.py`
2. **New Service**: Create in `src/services/`
3. **New Data Source**: Add client to `src/core/`
4. **New Report Type**: Extend `src/services/report_service.py`

## 📊 API Endpoints

- `GET /` - Web dashboard
- `POST /generate_report/` - Start analysis with config
- `POST /generate_report_with_service_account/` - Start analysis with embedded service account
- `GET /task/{task_id}` - Check analysis progress
- `GET /download/{file_path}` - Download generated reports
- `GET /health` - Health check
- `GET /docs` - API documentation

## 🔍 Monitoring & Logging

- Structured logging with configurable levels
- Request/response logging for API calls
- Progress tracking for long-running tasks
- Error tracking with full stack traces
- Performance metrics for crawling operations

## 🚀 Deployment

### Production Considerations

1. **Environment Variables**: Use production values in `.env`
2. **Database**: Configure Supabase for production
3. **Security**: Restrict CORS origins
4. **Monitoring**: Set up log aggregation
5. **Scaling**: Use process managers like Gunicorn

### Docker Support (Future)

The modular structure makes it easy to containerize:

```dockerfile
FROM python:3.11-slim
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY src/ /app/src/
COPY public/ /app/public/
WORKDIR /app
CMD ["python", "api_refactored.py"]
```

## 🧪 Testing

The refactored structure makes testing much easier:

```python
# Example unit test
from src.core.crawler import WebCrawler

def test_extract_internal_links():
    crawler = WebCrawler()
    html = "<a href='/page1'>Link 1</a>"
    links = crawler.extract_internal_links("https://example.com", html)
    assert "https://example.com/page1" in links
```

## 📈 Performance

- **Async Operations**: Non-blocking I/O for better performance
- **Connection Pooling**: Reuse HTTP connections
- **Batch Processing**: Process data in chunks
- **Caching**: Cache frequently accessed data
- **Resource Management**: Proper cleanup of temporary files

This refactored version provides a solid foundation for future development and maintenance while maintaining all the original functionality.
