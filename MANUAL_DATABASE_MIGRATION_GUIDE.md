# Manual Database Migration Guide

## 🎯 Issue Summary

**Error:** `Could not find the 'domain_property' column of 'sites' in the schema cache`

**Root Cause:** The `sites` table is missing the configuration columns that the new site management features require.

**Current Schema:** Only has `id`, `domain`, and `created_at` columns
**Required Schema:** Needs `domain_property`, `ga_property_id`, `service_account_data`, `homepage`, and `last_updated` columns

## 🔧 **Manual Migration Steps**

### **Step 1: Open Supabase Dashboard**
1. **Go to** [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. **Sign in** to your account
3. **Select your project** (the one containing the sites table)

### **Step 2: Open SQL Editor**
1. **Click "SQL Editor"** in the left sidebar
2. **Click "New Query"** to create a new SQL script

### **Step 3: Run Migration SQL**
**Copy and paste this SQL into the editor:**

```sql
-- Database Migration: Add Configuration Columns to Sites Table

-- Add configuration columns to sites table
ALTER TABLE sites 
ADD COLUMN IF NOT EXISTS domain_property TEXT,
ADD COLUMN IF NOT EXISTS ga_property_id TEXT,
ADD COLUMN IF NOT EXISTS service_account_data JSONB,
ADD COLUMN IF NOT EXISTS homepage TEXT,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();

-- Update existing sites to have a last_updated timestamp
UPDATE sites 
SET last_updated = created_at 
WHERE last_updated IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sites_domain_property ON sites(domain_property);
CREATE INDEX IF NOT EXISTS idx_sites_ga_property_id ON sites(ga_property_id);

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'sites' 
ORDER BY ordinal_position;
```

### **Step 4: Execute the Migration**
1. **Click "Run"** button (or press Ctrl+Enter)
2. **Wait for completion** - should see "Success" message
3. **Check the results** - should show the new table structure

### **Step 5: Verify Migration Success**
**Run this verification query:**

```sql
-- Check current table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'sites' 
ORDER BY ordinal_position;
```

**Expected Result:**
```
column_name          | data_type                   | is_nullable
---------------------|-----------------------------|-----------
id                   | integer                     | NO
domain               | text                        | YES
created_at           | timestamp with time zone    | YES
domain_property      | text                        | YES
ga_property_id       | text                        | YES
service_account_data | jsonb                       | YES
homepage             | text                        | YES
last_updated         | timestamp with time zone    | YES
```

### **Step 6: Check Existing Data**
**Run this query to see current sites:**

```sql
-- Check existing sites and their configuration status
SELECT 
    id,
    domain,
    domain_property,
    ga_property_id,
    homepage,
    CASE 
        WHEN domain_property IS NOT NULL AND ga_property_id IS NOT NULL 
        THEN 'Configured' 
        ELSE 'Needs Configuration' 
    END as status,
    created_at,
    last_updated
FROM sites
ORDER BY id;
```

**Expected Result for boernevisioncenter.com:**
```
id | domain                  | domain_property | ga_property_id | homepage | status              | created_at           | last_updated
---|-------------------------|-----------------|----------------|----------|---------------------|---------------------|-------------
1  | boernevisioncenter.com  | null           | null           | null     | Needs Configuration | 2025-06-24T17:14... | 2025-06-24T17:14...
```

## 🚀 **After Migration**

### **Step 1: Restart API Server**
1. **Stop the current server** (Ctrl+C in terminal)
2. **Start it again:**
   ```bash
   cd C:/Gautam/Projects/Scraper
   python api_refactored.py
   ```

### **Step 2: Test Configuration Update**
1. **Open** http://localhost:8000
2. **Click gear (⚙️) on boernevisioncenter.com**
3. **Click "Edit Configuration"**
4. **Fill in the form:**
   - Domain Property: `https://boernevisioncenter.com/`
   - GA Property ID: `[Your actual GA property ID]`
   - Upload service account JSON file
   - Homepage: `https://boernevisioncenter.com/`
5. **Click "Save Changes"**

### **Expected Success Response:**
```json
{
  "success": true,
  "message": "Configuration updated successfully for boernevisioncenter.com",
  "domain": "boernevisioncenter.com",
  "site_id": "1"
}
```

### **Step 3: Verify Configuration Saved**
**Run this SQL in Supabase to verify:**

```sql
-- Check that configuration was saved
SELECT 
    id,
    domain,
    domain_property,
    ga_property_id,
    homepage,
    CASE 
        WHEN service_account_data IS NOT NULL 
        THEN 'Has Service Account' 
        ELSE 'No Service Account' 
    END as service_account_status,
    last_updated
FROM sites 
WHERE id = 1;
```

**Expected Result After Configuration:**
```
id | domain                  | domain_property                    | ga_property_id | homepage                           | service_account_status | last_updated
---|-------------------------|------------------------------------|--------------  |------------------------------------|------------------------|-------------
1  | boernevisioncenter.com  | https://boernevisioncenter.com/   | *********      | https://boernevisioncenter.com/   | Has Service Account    | 2025-06-30T...
```

## 🔍 **Troubleshooting**

### **If Migration Fails**
1. **Check permissions** - Make sure you have admin access to the Supabase project
2. **Try individual commands** - Run each ALTER TABLE statement separately
3. **Check existing columns** - Some columns might already exist

### **If Configuration Update Still Fails**
1. **Check server logs** for detailed error messages
2. **Verify columns exist** using the verification query
3. **Test with site info endpoint:**
   ```bash
   curl "http://localhost:8000/sites/1/info"
   ```

### **If Site Shows Wrong Status**
1. **Refresh the page** - Browser cache might show old data
2. **Check database directly** using the verification queries
3. **Restart API server** to clear any caching

## 📊 **Migration Summary**

### **Before Migration**
```
sites table:
├── id (integer, primary key)
├── domain (text)
└── created_at (timestamp)
```

### **After Migration**
```
sites table:
├── id (integer, primary key)
├── domain (text)
├── created_at (timestamp)
├── domain_property (text)        ← NEW
├── ga_property_id (text)         ← NEW
├── service_account_data (jsonb)  ← NEW
├── homepage (text)               ← NEW
└── last_updated (timestamp)      ← NEW
```

### **Benefits Gained**
- ✅ **Configuration storage** - Sites can store setup details
- ✅ **One-click re-analysis** - No need to re-enter details
- ✅ **Site management** - Edit, update, clear configurations
- ✅ **Better user experience** - Proper site lifecycle management

## 🎉 **Expected Final Result**

After successful migration and configuration:

1. **boernevisioncenter.com shows "✅ Configured" badge**
2. **"Re-analyze" button works** with one click
3. **"Edit Configuration" works** without errors
4. **All new sites** automatically have proper configuration storage
5. **Site management features** fully functional

**The database will be properly set up to support the new site management workflows!** 🚀
