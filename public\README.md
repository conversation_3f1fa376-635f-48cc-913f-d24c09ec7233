# SEO Analysis Dashboard

This is a web interface for the SEO Analysis API. It allows you to:

1. Configure and run SEO analysis jobs
2. Monitor analysis progress in real-time
3. Download generated reports
4. Store results in Supabase

## Setup

1. Start the API server:
   ```bash
   python api.py serve
   ```

2. Open `index.html` in your browser or serve it with a static file server:
   ```bash
   python -m http.server 8080
   ```

3. Access the dashboard at http://localhost:8080

## Features

- **Configuration Form**: Set up your analysis with domain, Google Analytics, and date range
- **Service Account Upload**: Securely upload your Google service account credentials
- **Real-time Progress**: Monitor the analysis progress with live updates
- **Supabase Integration**: Optionally store results in your Supabase database
- **Recent Reports**: Quick access to your most recent analysis reports
- **Help Resources**: Built-in documentation for getting started

## Configuration Options

- **Domain Property**: Your domain in Google Search Console (e.g., https://example.com/)
- **GA Property ID**: Your Google Analytics 4 property ID
- **Homepage URL**: Optional homepage URL for crawling (defaults to domain property)
- **Service Account JSON**: Google service account credentials file
- **Date Range**: Optional start and end dates for data collection
- **Website URLs**: Optional specific URLs to crawl (one per line)
- **WordPress API Key**: Optional API key for WordPress Data Exporter plugin
- **Supabase Integration**: Optional Supabase URL and API key for data storage

## Security Notes

- The service account JSON is processed client-side and sent securely to the API
- No credentials are stored in browser storage
- For production use, implement proper authentication and HTTPS