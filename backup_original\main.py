import os
import json
import sys
import requests
import asyncio
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright
from google.oauth2 import service_account
from googleapiclient import discovery
import pandas as pd
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import re
from html import unescape
from markdownify import markdownify as md
import logging
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import Supabase client if available
try:
    from supabase import create_client
    import hashlib
    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("Supabase client not available. Install with: pip install supabase")
    SUPABASE_AVAILABLE = False

# Constants
WP_API_NAMESPACE = 'wp-data-exporter/v1'

# Global config variable for use in functions
config = {}

# Supabase client class
class SupabaseClient:
    def __init__(self, url, key, domain):
        """Initialize Supabase client with credentials and domain"""
        self.client = create_client(url, key)
        self.domain = domain
        
        # Generate a site_id based on domain
        self.site_id = hashlib.md5(domain.encode()).hexdigest()
        
        # Check if site exists, create if not
        self._ensure_site_exists()
        
    def _ensure_site_exists(self):
        """Make sure the site exists in the sites table"""
        try:
            # Check if site exists by domain instead of site_id
            response = self.client.table('sites').select('*').eq('domain', self.domain).execute()
            
            if not response.data:
                # Create site record
                site_data = {
                    'domain': self.domain,
                    'created_at': datetime.now().isoformat()
                }
                self.client.table('sites').insert(site_data).execute()
                logger.info(f"Created new site record for {self.domain}")
                
                # Get the assigned id
                response = self.client.table('sites').select('id').eq('domain', self.domain).execute()
                if response.data:
                    self.db_id = response.data[0]['id']
                else:
                    raise Exception(f"Failed to retrieve ID for newly created site: {self.domain}")
            else:
                # Store the database ID
                self.db_id = response.data[0]['id']
                
                # Update last_updated if it exists
                try:
                    self.client.table('sites').update({
                        'last_updated': datetime.now().isoformat()
                    }).eq('id', self.db_id).execute()
                except Exception:
                    # If last_updated doesn't exist, just continue
                    pass
                    
        except Exception as e:
            logger.error(f"Error ensuring site exists: {e}")
            # Set a default ID to avoid further errors
            self.db_id = None
    
    def save_pages_data(self, df):
        """Save pages data to Supabase"""
        if df.empty or self.db_id is None:
            return
            
        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id  # Use the database ID instead of the generated site_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')
        
        # Convert numeric columns to integers to avoid type errors
        for col in ['GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views', 'Title Length']:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        try:
            response = self.client.table('pages').upsert(
                records,
                on_conflict='site_id,URL,snapshot_date'
            ).execute()
            
            logger.info(f"Saved {len(records)} pages to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving pages data: {e}")
            return None
    
    def save_gsc_keywords(self, df):
        """Save GSC keywords data to Supabase in batches"""
        if df.empty or self.db_id is None:
            return
        
        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id
        
        # Convert numeric columns to appropriate types
        for col in ['Clicks', 'Impressions']:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)
        
        # Process in batches of 1000 records
        batch_size = 1000
        total_records = len(df)
        total_saved = 0
        
        for i in range(0, total_records, batch_size):
            batch_df = df.iloc[i:i+batch_size]
            records = batch_df.to_dict(orient='records')
            
            try:
                response = self.client.table('gsc_keywords').upsert(
                    records,
                    on_conflict='site_id,URL,Keyword,Month'
                ).execute()
                
                batch_saved = len(records)
                total_saved += batch_saved
                logger.info(f"Saved batch of {batch_saved} GSC keywords to Supabase ({total_saved}/{total_records})")
            except Exception as e:
                logger.error(f"Error saving GSC keywords batch: {e}")
        
        logger.info(f"Completed saving {total_saved} GSC keywords to Supabase")
        return total_saved
    
    def save_gsc_traffic(self, df):
        """Save GSC traffic data to Supabase"""
        if df.empty or self.db_id is None:
            return
            
        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id
        
        # Convert numeric columns to appropriate types
        for col in ['Clicks', 'Impressions']:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        try:
            response = self.client.table('gsc_traffic').upsert(
                records,
                on_conflict='site_id,URL,Month'
            ).execute()
            
            logger.info(f"Saved {len(records)} GSC traffic records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GSC traffic data: {e}")
            return None
    
    def save_internal_links(self, df):
        """Save internal links data to Supabase"""
        if df.empty or self.db_id is None:
            return
        
        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')
        
        # Add link_hash if it doesn't exist
        if 'link_hash' not in df.columns:
            df['link_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row['URL']}|{row['Target Hyperlink']}".encode()).hexdigest(), 
                axis=1
            )
        
        # Replace NaN values with None (which converts to SQL NULL)
        df = df.replace({float('nan'): None})
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        try:
            response = self.client.table('internal_links').upsert(
                records,
                on_conflict='site_id,link_hash,snapshot_date'
            ).execute()
            
            logger.info(f"Saved {len(records)} internal links to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving internal links: {e}")
            return None
    
    def save_ga_data(self, df):
        """Save GA data to Supabase"""
        if df.empty or self.db_id is None:
            return
        
        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id
        
        # Rename columns to match the database schema
        # If 'activeUsers' is not in the schema, rename it to a column that exists
        # or drop it if it's not needed
        column_mapping = {
            'activeUsers': 'active_users',  # Assuming the column is named 'active_users' in the database
            'screenPageViews': 'page_views'  # Assuming the column is named 'page_views' in the database
        }
        
        # Apply the mapping for columns that exist in the DataFrame
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df[new_col] = df[old_col]
                df = df.drop(columns=[old_col])
        
        # Convert numeric columns to appropriate types
        for col in ['page_views', 'active_users']:
            if col in df.columns:
                df[col] = df[col].fillna(0).astype(int)
        
        # Replace NaN values with None
        df = df.replace({float('nan'): None})
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        try:
            response = self.client.table('ga_data').upsert(
                records,
                on_conflict='site_id,URL,Month'
            ).execute()
            
            logger.info(f"Saved {len(records)} GA records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GA data: {e}")
            
            # If the error is about column names, log the available columns
            if "Could not find" in str(e) and "column" in str(e):
                try:
                    # Try to get the table schema
                    schema_response = self.client.table('ga_data').select('*').limit(1).execute()
                    if schema_response.data:
                        available_columns = list(schema_response.data[0].keys())
                        logger.error(f"Available columns in ga_data: {available_columns}")
                        logger.error(f"Columns in DataFrame: {list(df.columns)}")
                except Exception as schema_e:
                    logger.error(f"Could not retrieve schema: {schema_e}")
            
            return None
    
    def generate_excel_report(self, output_dir, date_filter=None):
        """Generate Excel report from Supabase data"""
        logger.info(f"Generating Excel report from Supabase data for {self.domain}")
        
        if self.db_id is None:
            logger.error("Cannot generate report: No database ID for site")
            return None
        
        try:
            # Build queries
            pages_query = self.client.table('pages').select('*').eq('site_id', self.db_id)
            keywords_query = self.client.table('gsc_keywords').select('*').eq('site_id', self.db_id)
            traffic_query = self.client.table('gsc_traffic').select('*').eq('site_id', self.db_id)
            links_query = self.client.table('internal_links').select('*').eq('site_id', self.db_id)
            ga_query = self.client.table('ga_data').select('*').eq('site_id', self.db_id)
            
            # Apply date filter if provided
            if date_filter:
                pages_query = pages_query.eq('snapshot_date', date_filter)
                links_query = links_query.eq('snapshot_date', date_filter)
            
            # Execute queries
            try:
                pages_response = pages_query.execute()
                keywords_response = keywords_query.execute()
                traffic_response = traffic_query.execute()
                links_response = links_query.execute()
                ga_response = ga_query.execute()
                
                # Convert to DataFrames
                data_df = pd.DataFrame(pages_response.data) if pages_response.data else pd.DataFrame()
                keywords_df = pd.DataFrame(keywords_response.data) if keywords_response.data else pd.DataFrame()
                hist_traffic_df = pd.DataFrame(traffic_response.data) if traffic_response.data else pd.DataFrame()
                internal_links_df = pd.DataFrame(links_response.data) if links_response.data else pd.DataFrame()
                ga_df = pd.DataFrame(ga_response.data) if ga_response.data else pd.DataFrame()
                
                # Create Excel file
                date_str = f"_{datetime.now().strftime('%Y%m%d')}"
                excel_path = os.path.join(output_dir, f'report_{self.domain.replace(".", "_")}{date_str}.xlsx')
                
                with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
                    # Write each DataFrame to a different sheet
                    if not data_df.empty:
                        data_df.to_excel(writer, sheet_name='Data', index=False)
                    if not keywords_df.empty:
                        keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                    if not hist_traffic_df.empty:
                        hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
                    if not internal_links_df.empty:
                        internal_links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
                    if not ga_df.empty:
                        ga_df.to_excel(writer, sheet_name='GA-Data', index=False)
                
                logger.info(f"Excel report generated: {excel_path}")
                return excel_path
            except Exception as e:
                logger.error(f"Error generating Excel report: {e}")
                return None
        except Exception as e:
            logger.error(f"Error in generate_excel_report: {e}")
            return None

    def discover_table_schema(self, table_name):
        """Discover the schema of a table by fetching a sample row"""
        try:
            response = self.client.table(table_name).select('*').limit(1).execute()
            if response.data:
                columns = list(response.data[0].keys())
                logger.info(f"Discovered columns for table '{table_name}': {columns}")
                return columns
            else:
                # Try to get column info by inserting and then rolling back
                # This is a hack but might work if the table is empty
                logger.info(f"Table '{table_name}' appears to be empty, trying to discover schema...")
                try:
                    # Create a minimal record with just site_id
                    test_record = {'site_id': self.db_id, '_test': True}
                    # Use RPC to call a function that returns the column names
                    # This requires setting up a stored procedure in your database
                    response = self.client.rpc('get_table_columns', {'table_name': table_name}).execute()
                    if response.data:
                        logger.info(f"Discovered columns for table '{table_name}' via RPC: {response.data}")
                        return response.data
                    else:
                        logger.warning(f"Could not discover schema for empty table '{table_name}'")
                        return []
                except Exception as e:
                    logger.warning(f"Schema discovery attempt failed: {e}")
                    return []
        except Exception as e:
            logger.error(f"Error discovering schema for table '{table_name}': {e}")
            return []

    def initialize_database(self):
        """Initialize the database schema if tables don't exist"""
        try:
            # Check if tables exist
            tables = ['sites', 'pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
            missing_tables = []
            
            for table in tables:
                try:
                    response = self.client.table(table).select('count').limit(1).execute()
                    logger.info(f"Table '{table}' exists")
                except Exception:
                    missing_tables.append(table)
            
            if missing_tables:
                logger.warning(f"Missing tables: {missing_tables}")
                # You could create the tables here if needed
            
            return len(missing_tables) == 0
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            return False



# Function to detect and fetch WordPress API data
def detect_wp_api(site_url):
    """
    Detects if the site has the WP Data Exporter plugin installed and returns data if available.
    
    Args:
        site_url: Base URL of the website
    
    Returns:
        dict: API response data if successful, None otherwise
    """
    print(f"[INFO] Checking if WordPress Data Exporter plugin is available at {site_url}")
    
    # Normalize site URL
    if not site_url.endswith('/'):
        site_url = site_url + '/'
    
    # Try common API endpoints
    wp_api_endpoints = [
        f"{site_url}wp-json/{WP_API_NAMESPACE}/data",
        f"{site_url}index.php/wp-json/{WP_API_NAMESPACE}/data"
    ]
    
    for endpoint in wp_api_endpoints:
        try:
            # First try without API key
            response = requests.get(endpoint, timeout=15)
            
            # If we get a 401 Unauthorized, it means the API exists but needs a key
            if response.status_code == 401:
                print(f"[INFO] WordPress API detected at {endpoint} but requires authentication.")
                # Check if wp_api_key is in config
                if 'wp_api_key' in config and config['wp_api_key']:
                    print(f"[INFO] Trying with provided API key")
                    headers = {'X-Plugin-API-Key': config['wp_api_key']}
                    response = requests.get(endpoint, headers=headers, timeout=15)
                    if response.status_code == 200:
                        print(f"[INFO] Successfully connected to WordPress API with provided key.")
                        return response.json()
                return None
            
            # If we get a 200 OK, the API exists and is public
            if response.status_code == 200:
                print(f"[INFO] WordPress API detected and accessible at {endpoint}")
                return response.json()
                
        except requests.exceptions.RequestException as e:
            print(f"[DEBUG] API endpoint {endpoint} not available: {e}")
            continue
    
    print(f"[INFO] WordPress Data Exporter plugin not detected. Will use crawling instead.")
    return None

# Function to convert HTML to clean markdown
# def html_to_markdown(html_content):
#     """
#     Converts HTML to clean markdown, removing unnecessary elements.
    
#     Args:
#         html_content: Raw HTML content
        
#     Returns:
#         str: Clean markdown content
#     """
#     if not html_content:
#         return ""
    
#     # Create BeautifulSoup object
#     soup = BeautifulSoup(html_content, 'html.parser')
    
#     # Remove script, style, nav, footer, header, and other non-content elements
#     for element in soup.select('script, style, nav, footer, header, .sidebar, .widget, .menu, .comments, .comment-form, .footer, .header'):
#         element.decompose()
    
#     # Get the main content if available
#     main_content = soup.find('main') or soup.find('article') or soup.find('div', class_='content') or soup
    
#     # Convert to plain text first to remove all HTML formatting
#     text = main_content.get_text(separator='\n', strip=True)
    
#     # Clean up the text
#     text = re.sub(r'\n{3,}', '\n\n', text)  # Remove excessive newlines
    
#     return text

def html_to_markdown(html_content):
    """
    Converts HTML to clean Markdown using markdownify, removing unnecessary elements.

    Args:
        html_content: Raw HTML content

    Returns:
        str: Clean markdown content
    """
    if not html_content:
        return ""

    # Parse the HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # Remove unwanted elements
    for element in soup.select('script, style, nav, footer, header, .sidebar, .widget, .menu, .comments, .comment-form, .footer, .header'):
        element.decompose()

    # Focus on main content
    main_content = soup.find('main') or soup.find('article') or soup.find('div', class_='content') or soup

    # Convert the remaining HTML to Markdown
    markdown = md(str(main_content), strip=['a', 'img'])  # Optional: strip links and images

    # Remove excessive newlines
    markdown = re.sub(r'\n{3,}', '\n\n', markdown)

    return markdown

# --- Step 1: Crawl Website ---
async def get_js_rendered_html(url):
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        await page.goto(url, timeout=60000)  # Increase timeout to 60 seconds

        content = await page.content()
        await browser.close()
        return content

def extract_internal_links(base_url, html):
    """
    Extracts all internal links from HTML content.
    
    Args:
        base_url: Base URL of the website
        html: HTML content to parse
    
    Returns:
        list: List of internal links
    """
    soup = BeautifulSoup(html, 'html.parser')
    domain = urlparse(base_url).netloc
    links = set()
    
    for a_tag in soup.find_all('a', href=True):
        href = a_tag['href']
        full_url = urljoin(base_url, href)
        parsed_url = urlparse(full_url)
        
        # Only include URLs from the same domain and exclude fragments
        if parsed_url.netloc == domain:
            # Remove fragments (#) from URLs
            clean_url = full_url.split('#')[0]
            # Remove trailing slash for consistency
            if clean_url.endswith('/') and clean_url != base_url + '/':
                clean_url = clean_url[:-1]
            links.add(clean_url)
    
    return list(links)

def crawl_url(url):
    try:        
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            print(f"Skipping dead link: {url} (Status code: {response.status_code})")
            return None
        
        html_content = response.text
        soup = BeautifulSoup(html_content, 'html.parser')
        title = soup.title.string if soup.title else 'No title'
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        h1 = soup.find('h1')        
        
        # Store both the raw HTML (for link extraction) and markdown (for display)
        return {
            'url': url,
            'title': title,
            'description': meta_desc['content'] if meta_desc else '',
            'h1': h1.text if h1 else '',
            'text': html_to_markdown(html_content),  # Markdown for storage
            'raw_html': html_content  # Keep raw HTML for link extraction
        }
    except Exception as e:
        print(f"[ERROR] Error crawling {url}: {e}")
        return None

def crawl_site(urls, output_dir):
    results = []
    failed_urls = []
    for url in urls:
        data = crawl_url(url)
        if not data:
            try:
                print(f"Trying JS rendering for {url}...")
                html = asyncio.run(get_js_rendered_html(url))
                soup = BeautifulSoup(html, 'html.parser')
                
                # Convert HTML to markdown for better LLM processing
                text = html_to_markdown(html)
                
                data = {
                    'url': url,
                    'title': soup.title.string if soup.title else 'No title',
                    'description': soup.find('meta', attrs={'name': 'description'})['content'] if soup.find('meta', attrs={'name': 'description'}) else '',
                    'h1': soup.find('h1').text if soup.find('h1') else '',
                    'text': text
                }
            except Exception as e:
                print(f"JS rendering failed for {url}: {e}")
                failed_urls.append(url)
                continue
        if data:
            results.append(data)

    if failed_urls:
        with open(os.path.join(output_dir, 'failed_urls.txt'), 'w') as f:
            for url in failed_urls:
                f.write(url + '\n')

    return results

def discover_urls_from_homepage(homepage):
    """
    Discovers all internal URLs from a homepage using JavaScript rendering.
    
    Args:
        homepage: Base URL of the website
    
    Returns:
        list: List of discovered URLs
    """
    print(f"[INFO] Discovering URLs from homepage {homepage}...")
    try:
        # Use JavaScript rendering to get the full HTML
        html = asyncio.run(get_js_rendered_html(homepage))
        
        # Extract all internal links
        links = extract_internal_links(homepage, html)
        
        # Add the homepage itself and remove duplicates
        all_urls = [homepage] + links
        unique_urls = list(dict.fromkeys(all_urls))  # Remove duplicates while preserving order
        
        print(f"[INFO] Discovered {len(unique_urls)} URLs: {unique_urls}")
        return unique_urls
    except Exception as e:
        print(f"[ERROR] Failed to discover URLs from homepage {homepage}: {e}")
        return [homepage]

# --- Step 2: Google Search Console Data (Monthly) ---
def get_gsc_data_by_month(site_url, start_date, end_date, credentials):
    print("\n[INFO] Fetching GSC data by month for", site_url, "from", start_date, "to", end_date)
    try:
        service = discovery.build('searchconsole', 'v1', credentials=credentials)
        verified_sites = _list_verified_sites(service)
        return _get_gsc_data_by_month(site_url, start_date, end_date, service, verified_sites)
    except Exception as e:
        print(f"GSC API initialization error: {e}")
        return pd.DataFrame()

def _list_verified_sites(service):
    print("\n[INFO] Listing all accessible GSC properties for validation:")
    response = service.sites().list().execute()
    print("\nAccessible Verified Properties:")
    for site in response.get('siteEntry', []):
        print("-", site['siteUrl'])
    return [site['siteUrl'] for site in response.get('siteEntry', [])]

def _get_gsc_data_by_month(site_url, start_date, end_date, service, verified_sites):
    if site_url not in verified_sites:
        alt_form = "sc-domain:" + urlparse(site_url).netloc.replace('www.', '')
        if alt_form in verified_sites:
            print(f"NOTE: Using domain property format '{alt_form}' instead of '{site_url}'")
            site_url = alt_form
        elif site_url.endswith('/') and site_url[:-1] in verified_sites:
            print(f"NOTE: Trying without trailing slash: '{site_url[:-1]}'")
            site_url = site_url[:-1]
        else:
            raise ValueError(
                f"Site '{site_url}' not found in verified GSC properties. "
                "Please check your permissions or property URL format."
            )

    data = []
    current = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")

    while current < end:
        next_month = current + relativedelta(months=1)
        request = {
            'startDate': current.strftime("%Y-%m-%d"),
            'endDate': next_month.strftime("%Y-%m-%d"),
            'dimensions': ['page', 'query'],
            'rowLimit': 25000
        }
        try:
            response = service.searchanalytics().query(siteUrl=site_url, body=request).execute()
            rows = response.get('rows', [])
        except Exception as e:
            print(f"GSC API error for {current.strftime('%Y-%m')}: {e}")
            rows = []

        for row in rows:
            page = row['keys'][0]
            keyword = row['keys'][1]
            clicks = row.get('clicks', 0)
            impressions = row.get('impressions', 0)
            ctr = row.get('ctr', 0)
            position = row.get('position', 0)
            data.append([
                page, keyword, clicks, impressions, ctr, position, current.strftime("%Y-%m")
            ])

        current = next_month

    return pd.DataFrame(
        data,
        columns=['URL', 'Keyword', 'Clicks', 'Impressions', 'CTR', 'Position', 'Month']
    )

# --- Step 3: Google Analytics Data (Monthly) ---
def get_ga_data_by_month(property_id, start_date, end_date, credentials):
    print("\n[INFO] Fetching GA data by month for", property_id, "from", start_date, "to", end_date)
    try:
        analytics = discovery.build('analyticsdata', 'v1beta', credentials=credentials)
        return _get_ga_data_by_month(property_id, start_date, end_date, analytics)
    except Exception as e:
        print(f"GA API initialization error: {e}")
        return pd.DataFrame()

def _get_ga_data_by_month(property_id, start_date, end_date, analytics):
    dimensions = [
        "landingPagePlusQueryString",
        "date",
        "country",
        "region",
        "pageTitle",
        "pageReferrer",
        "newVsReturning"
    ]
    metrics = ["activeUsers", "screenPageViews"]
    data = []

    current = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")

    while current < end:
        next_month = current + relativedelta(months=1)
        request_body = {
            "dateRanges": [
                {
                    "startDate": current.strftime("%Y-%m-%d"),
                    "endDate": next_month.strftime("%Y-%m-%d")
                }
            ],
            "dimensions": [{"name": name} for name in dimensions],
            "metrics": [{"name": name} for name in metrics]
        }
        try:
            response = analytics.properties().runReport(
                property=f"properties/{property_id}",
                body=request_body
            ).execute()
            rows = response.get('rows', [])
        except Exception as e:
            print(f"GA API error for {current.strftime('%Y-%m')}: {e}")
            rows = []

        for row in rows:
            dim_vals = [val['value'] for val in row.get('dimensionValues', [])]
            met_vals = [val['value'] for val in row.get('metricValues', [])]
            data.append(dim_vals + met_vals + [current.strftime("%Y-%m")])

        current = next_month

    columns = dimensions + metrics + ['Month']
    return pd.DataFrame(data, columns=columns)

def fetch_wp_api_data(wp_api_url, wp_api_key):
    print(f"[INFO] Fetching data from WordPress API: {wp_api_url}")
    headers = {'X-Plugin-API-Key': wp_api_key}
    try:
        response = requests.get(wp_api_url, headers=headers, timeout=60)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        print("[INFO] Successfully fetched data from WordPress API.")
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Could not fetch data from WordPress API: {e}")
        return None


def is_valid_date_format(date_string):
    try:
        datetime.strptime(date_string, "%Y-%m-%d")
        return True
    except ValueError: return False


def build_internal_links_sheet(crawl_results, data_df, wp_data=None):
    print("[INFO] Building internal links data...")
    
    # If we have WordPress API data with links, use it directly
    if wp_data and 'internal_links_data' in wp_data and wp_data['internal_links_data']:
        print(f"[INFO] Using {len(wp_data['internal_links_data'])} internal links from WordPress API")
        
        # Transform the API data format to match our expected output format
        internal_links_rows = []
        for link in wp_data['internal_links_data']:
            # Map the API fields to our expected fields
            internal_links_rows.append({
                'URL': link.get('source_page_link', ''),
                'Target Hyperlink': link.get('target_hyperlink', ''),
                'Anchor Text': link.get('anchor_text', ''),
                'Link Type': link.get('link_type', 'Unknown'),
                'URL Topic': '',  # Add topic if available
                'Relevance Score': link.get('relevance_score', None),
                'Target Title': link.get('target_page_title_if_internal', '')
            })
        
        return pd.DataFrame(internal_links_rows)
    
    # Otherwise, fall back to the enhanced HTML parsing method
    print("[INFO] No internal links data from API, falling back to enhanced HTML parsing")
    
    # Define exclusion lists similar to WordPress plugin
    excluded_anchor_texts = [
        'Read More', 'Continue Reading', 'Learn More', 'View More', 
        'Get in touch', 'Click here', 'Read now', 'Contact us'
    ]
    
    excluded_url_patterns = [
        '/blog/', '/news/', '/page/', '/tag/', '/category/', 
        '/author/', '/archive/', '/feed/', '/rss/', 
        '/wp-admin/', '/wp-content/', '/wp-includes/'
    ]
    
    internal_links_rows = []
    site_domain = None
    
    for page in crawl_results:
        src_url = page['url']
        
        # Set site domain if not already set
        if not site_domain:
            site_domain = urlparse(src_url).netloc
        
        html = page.get('raw_html')  # Use the stored raw HTML
        
        if not html:
            try:
                print(f"[INFO] No stored HTML for {src_url}, fetching...")
                response = requests.get(src_url, timeout=10)
                html = response.text
            except Exception as e:
                try:
                    print(f"[INFO] Trying JS rendering for {src_url}...")
                    html = asyncio.run(get_js_rendered_html(src_url))
                except Exception as e:
                    print(f"[ERROR] Failed to get HTML for {src_url}: {e}")
                    continue
        
        # Clean HTML similar to WordPress plugin
        soup = BeautifulSoup(html, 'html.parser')
        
        # Remove navigation elements
        for nav in soup.find_all(['nav', 'header', 'footer']):
            nav.decompose()
        
        # Remove buttons
        for button in soup.find_all('button'):
            button.decompose()
        
        # Remove links with button-like classes
        for a in soup.find_all('a', class_=lambda c: c and any(cls in c for cls in ['btn', 'button', 'cta'])):
            a.decompose()
        
        # Process remaining links
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            anchor_text = a_tag.get_text().strip()
            
            # Skip empty anchors
            if not anchor_text:
                continue
                
            # Skip excluded anchor texts
            if any(excluded.lower() in anchor_text.lower() for excluded in excluded_anchor_texts):
                continue
            
            # Skip special protocols
            if re.match(r'^(mailto|tel|sms|javascript):', href):
                continue
                
            # Skip TOC links
            if re.search(r'#(toc|tableofcontents|contents)', href, re.IGNORECASE):
                continue
            
            # Normalize URL
            full_url = urljoin(src_url, href)
            parsed_url = urlparse(full_url)
            
            # Skip excluded URL patterns
            if any(pattern in parsed_url.path for pattern in excluded_url_patterns):
                continue
            
            # Determine link type
            link_type = 'External'
            target_title = ''
            relevance_score = None
            
            if parsed_url.netloc == site_domain:
                if '#' in href:
                    link_type = 'Jump Link'
                else:
                    link_type = 'Internal'
                    
                    # Try to find target title for internal links
                    target_url_no_hash = full_url.split('#')[0]
                    for target_page in crawl_results:
                        if target_page['url'] == target_url_no_hash:
                            target_title = target_page.get('title', '')
                            
                            # Calculate relevance score
                            source_title = page.get('title', '')
                            relevance_score = calculate_relevance_score(
                                source_title, target_title, anchor_text, src_url, target_url_no_hash
                            )
                            break
            
            # Get topic if available
            topic = ''
            if 'Topic' in data_df.columns and src_url in data_df['URL'].values:
                topic = data_df.loc[data_df['URL'] == src_url, 'Topic'].values[0]
            
            internal_links_rows.append({
                'URL': src_url,
                'Target Hyperlink': full_url.split('#')[0],  # Remove fragment
                'Anchor Text': anchor_text,
                'Link Type': link_type,
                'URL Topic': topic,
                'Target Title': target_title,
                'Relevance Score': relevance_score
            })
    
    return pd.DataFrame(internal_links_rows)

# Add a relevance score calculation function similar to WordPress plugin
def calculate_relevance_score(source_title, target_title, anchor_text, source_url, target_url):
    """
    Calculate relevance score between source and target pages.
    Higher score means more relevant link.
    """
    score = 0
    
    # 1. Check if anchor text appears in target title (strongest signal)
    if anchor_text and target_title:
        anchor_words = set(re.findall(r'\b\w{3,}\b', anchor_text.lower()))
        title_words = set(re.findall(r'\b\w{3,}\b', target_title.lower()))
        
        common_words = anchor_words.intersection(title_words)
        if common_words:
            # More matching words = higher score
            score += len(common_words) * 5
    
    # 2. Check if source and target titles share words (topic similarity)
    if source_title and target_title:
        source_words = set(re.findall(r'\b\w{3,}\b', source_title.lower()))
        target_words = set(re.findall(r'\b\w{3,}\b', target_title.lower()))
        
        common_words = source_words.intersection(target_words)
        if common_words:
            score += len(common_words) * 2
    
    # 3. URL path similarity (pages in same section)
    source_path = urlparse(source_url).path.strip('/')
    target_path = urlparse(target_url).path.strip('/')
    
    source_dirs = source_path.split('/')
    target_dirs = target_path.split('/')
    
    # If they share parent directories
    for i in range(min(len(source_dirs), len(target_dirs))):
        if source_dirs[i] == target_dirs[i]:
            score += 1
        else:
            break
    
    return score

# Load environment variables from .env file
load_dotenv()

# --- Main Script Execution ---
if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: python main6.py <config_file.json>")
        sys.exit(1)

    config_path = sys.argv[1]
    # Load config
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Check for Supabase credentials in environment variables first, then config file
    supabase_url = os.getenv('SUPABASE_URL') or config.get('supabase_url')
    supabase_key = os.getenv('SUPABASE_KEY') or config.get('supabase_key')
    
    # Add Supabase credentials to config if found in environment
    if supabase_url:
        config['supabase_url'] = supabase_url
    if supabase_key:
        config['supabase_key'] = supabase_key
    
    # Basic config validation
    domain_property = config['domain_property']
    ga_property_id = config['ga_property_id']
    service_account_file = config['service_account_file']
    
    # optional ones
    homepage = config.get('homepage') or ("https://" + urlparse(domain_property).netloc)
    date_from = config.get('start_date')
    date_to = config.get('end_date')    
    
    if date_from is None:
        date_from = (datetime.today() - timedelta(days=365)).strftime('%Y-%m-%d')
    if date_to is None:
        date_to = datetime.today().strftime('%Y-%m-%d')

    if not is_valid_date_format(date_from) or not is_valid_date_format(date_to):
        print("[ERROR] Invalid date format. Dates must be in the form YYYY-MM-DD")
        sys.exit(1)

    # --- Step 1: Check for WordPress API or fallback to crawling ---
    crawl_results = []
    if 'website_urls' in config and config['website_urls']:
        website_urls = config['website_urls']
        print(f"[INFO] Using {len(website_urls)} URLs provided in configuration")
    else:
        # Try to detect and use WordPress API
        wp_data = detect_wp_api(homepage)

        if wp_data and 'publish_seo_data' in wp_data:
            print(f"[INFO] Using WordPress API data for content analysis. Found {len(wp_data['publish_seo_data'])} pages.")
            crawl_results = wp_data['publish_seo_data']
            # Extract URLs for potential additional crawling
            website_urls = [item['url'] for item in crawl_results]
        else:
            print("[INFO] WordPress API not available. Falling back to crawling.")
            
            # Discover URLs by crawling
            website_urls = discover_urls_from_homepage(homepage)

    credentials = service_account.Credentials.from_service_account_file(service_account_file)
    output_dir = f"reports_{urlparse(domain_property).netloc.replace('.', '_')}"
    os.makedirs(output_dir, exist_ok=True)

    # Save the list of URLs to crawl for debugging
    with open(os.path.join(output_dir, 'urls_to_crawl.txt'), 'w') as f:
        for url in website_urls:
            f.write(f"{url}\n")

    # Only crawl if we don't have WP API data or if it's limited
    if not crawl_results or len(crawl_results) <= 1:
        print(f"[INFO] Crawling {len(website_urls)} URLs...")
        crawl_results = crawl_site(website_urls, output_dir)
    
    # Process crawl results
    for page in crawl_results:
        if 'text' in page and page['text']:
            # Convert HTML content to clean markdown
            page['text'] = html_to_markdown(page['text'])
    
    crawl_df = pd.DataFrame(crawl_results)

    # Rename and prepare crawl_df for merge
    crawl_df = crawl_df.rename(columns={
        'url': 'URL',
        'title': 'SEO Title',
        'description': 'Meta Description',
        'h1': 'H1',
        'text': 'Page Content'
    })
    crawl_df['Title Length'] = crawl_df['SEO Title'].map(lambda t: len(t) if isinstance(t, str) else 0)
    # Placeholder columns; populate via WP API if available
    crawl_df['Focus Keyword'] = ''
    crawl_df['Page Type'] = ''
    crawl_df['Topic'] = ''

    # --- Google Search Console (Monthly) ---
    gsc_df = get_gsc_data_by_month(domain_property, date_from, date_to, credentials)
    # Ensure we have the expected columns even if empty
    expected_gsc_cols = ['URL', 'Keyword', 'Clicks', 'Impressions', 'CTR', 'Position', 'Month']
    for col in expected_gsc_cols:
        if col not in gsc_df.columns:
            gsc_df[col] = []

    keywords_df = gsc_df.copy()  # for “Keywords” sheet

    # Build “Historical-Traffic” sheet (aggregate GSC by URL, Month)
    if not gsc_df.empty:
        hist_traffic_df = (
            gsc_df.groupby(['URL', 'Month'], as_index=False)
                 .agg({'Clicks': 'sum', 'Impressions': 'sum'})
        )
        hist_traffic_df['CTR'] = hist_traffic_df.apply(
            lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
            axis=1
        )
        pos_weight = (
            gsc_df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
                 .groupby(['URL', 'Month'], as_index=False)
                 .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
        )
        pos_weight = pos_weight.rename(columns={'Impressions': 'IMP_sum'})
        hist_traffic_df = hist_traffic_df.merge(
            pos_weight[['URL', 'Month', 'weighted_pos', 'IMP_sum']],
            on=['URL', 'Month'],
            how='left'
        ).fillna({'weighted_pos': 0, 'IMP_sum': 1})
        hist_traffic_df['Position'] = hist_traffic_df.apply(
            lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
            axis=1
        )
        hist_traffic_df = hist_traffic_df.drop(columns=['weighted_pos', 'IMP_sum'])
    else:
        hist_traffic_df = pd.DataFrame(columns=['URL', 'Month', 'Clicks', 'Impressions', 'CTR', 'Position'])

    # Build total GSC per URL for “Data” sheet
    if not gsc_df.empty:
        gsc_total_df = (
            gsc_df.groupby('URL', as_index=False)
                  .agg({'Clicks': 'sum', 'Impressions': 'sum'})
        )
        gsc_total_df['CTR'] = gsc_total_df.apply(
            lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
            axis=1
        )

        pos_weight_all = (
            gsc_df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
                  .groupby('URL', as_index=False)
                  .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
        )
        pos_weight_all = pos_weight_all.rename(columns={'Impressions': 'IMP_sum'})
        gsc_total_df = gsc_total_df.merge(
            pos_weight_all[['URL', 'weighted_pos', 'IMP_sum']],
            on='URL',
            how='left'
        ).fillna({'weighted_pos': 0, 'IMP_sum': 1})
        gsc_total_df['Position'] = gsc_total_df.apply(
            lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
            axis=1
        )
        gsc_total_df = gsc_total_df.drop(columns=['weighted_pos', 'IMP_sum'])
        gsc_total_df = gsc_total_df.rename(columns={
            'Clicks': 'GSC Clicks',
            'Impressions': 'GSC Impressions'
        })
    else:
        gsc_total_df = pd.DataFrame(columns=[
            'URL', 'GSC Clicks', 'GSC Impressions', 'CTR', 'Position'
        ])

    # --- Google Analytics (Monthly) ---
    ga_df = get_ga_data_by_month(ga_property_id, date_from, date_to, credentials)
    expected_ga_cols = [
        "landingPagePlusQueryString", "date", "country", "region",
        "pageTitle", "pageReferrer", "newVsReturning",
        "activeUsers", "screenPageViews", "Month"
    ]
    for col in expected_ga_cols:
        if col not in ga_df.columns:
            ga_df[col] = ""

    # 1) Keep only valid paths (those starting with "/"), drop anything else
    if not ga_df.empty:
        ga_df = ga_df[ga_df['landingPagePlusQueryString'].str.startswith('/')].copy()

        # 2) Cast metrics to int BEFORE grouping
        ga_df['screenPageViews'] = ga_df['screenPageViews'].astype(int)
        ga_df['activeUsers']     = ga_df['activeUsers'].astype(int)

        # 3) Build the full-URL column so it matches crawl_df["URL"]
        base = homepage.rstrip('/')  # e.g. "https://wpsuites.com"
        ga_df['URL'] = base + ga_df['landingPagePlusQueryString']

        # 4) Aggregate per-URL for the “Data” sheet
        ga_total_df = (
            ga_df.groupby('URL', as_index=False)
                 .agg({'screenPageViews': 'sum', 'activeUsers': 'sum'})
                 .rename(columns={
                     'screenPageViews': 'Google Analytics Page Views',
                     'activeUsers':     'Active Users'
                 })
        )
    else:
        ga_total_df = pd.DataFrame(columns=[
            'URL', 'Google Analytics Page Views', 'Active Users'
        ])

    # --- Merge into “Data” sheet DataFrame ---
    data_df = crawl_df.merge(gsc_total_df, on='URL', how='left')
    data_df = data_df.merge(ga_total_df, on='URL', how='left')

    # Fill NaNs with 0 for numeric metrics
    for col in ['GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views', 'Active Users']:
        if col in data_df.columns:
            data_df[col] = data_df[col].fillna(0)
            data_df[col] = data_df[col].infer_objects(copy=False)  # Add this line to address the warning

    # Reorder columns to match desired “Data” layout
    data_columns = [
        'URL',
        'GSC Clicks',
        'GSC Impressions',
        'Google Analytics Page Views',
        'Focus Keyword',
        'Page Type',
        'Topic',
        'Page Content',
        'SEO Title',
        'Title Length',
        'Meta Description',
        'H1'
    ]
    data_df = data_df[[col for col in data_columns if col in data_df.columns]]

    # --- Build "Internal-Links" sheet ---
    internal_links_df = build_internal_links_sheet(crawl_results, data_df, wp_data)

    # --- Check for Supabase credentials and save data ---
    if SUPABASE_AVAILABLE and 'supabase_url' in config and 'supabase_key' in config:
        print(f"[INFO] Saving data to Supabase...")
        try:
            # Initialize Supabase client
            domain = urlparse(domain_property).netloc
            supabase_client = SupabaseClient(
                url=config['supabase_url'],
                key=config['supabase_key'],
                domain=domain
            )
            
            # Save all data to Supabase
            supabase_client.save_pages_data(data_df)
            supabase_client.save_gsc_keywords(keywords_df)
            supabase_client.save_gsc_traffic(hist_traffic_df)
            supabase_client.save_internal_links(internal_links_df)
            
            # Also save GA data if available
            if not ga_df.empty:
                supabase_client.save_ga_data(ga_df)
            
            # Generate Excel report from Supabase data
            excel_path = supabase_client.generate_excel_report(output_dir)
            print(f"[INFO] Excel report generated from Supabase data: {excel_path}")
            
        except Exception as e:
            print(f"[ERROR] Supabase error: {e}")
            print("[INFO] Falling back to direct Excel generation...")
            
            # Fall back to direct Excel generation
            excel_path = os.path.join(output_dir, 'final_report.xlsx')
            with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
                data_df.to_excel(writer, sheet_name='Data', index=False)
                keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
                internal_links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
            
            print(f"[INFO] Excel report generated directly: {excel_path}")
    else:
        # If no Supabase credentials, generate Excel directly
        print("[INFO] No Supabase credentials provided. Generating Excel report directly...")
        excel_path = os.path.join(output_dir, 'final_report.xlsx')
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            data_df.to_excel(writer, sheet_name='Data', index=False)
            keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
            hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
            internal_links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
        
        print(f"[INFO] Excel report generated: {excel_path}")

    print(f"All reports generated in folder: {output_dir}")
    print(f"[INFO] Final Excel report: {excel_path}")

