#!/usr/bin/env python3
"""
Test script to debug the clear site data issue
"""
import os
import sys
import json
from datetime import datetime

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import settings

def test_clear_data_operation():
    """Test the clear data operation to see what's happening"""
    
    # Check if Supabase credentials are available
    if not settings.supabase_url or not settings.supabase_key:
        print("❌ Supabase credentials not configured")
        return
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_key)
        
        # Get all sites to see what we have
        sites_response = client.table('sites').select('*').execute()
        
        if not sites_response.data:
            print("❌ No sites found in database")
            return
        
        print(f"📊 Found {len(sites_response.data)} sites:")
        for site in sites_response.data:
            print(f"  - ID: {site['id']}, Domain: {site['domain']}")
            print(f"    Has domain_property: {bool(site.get('domain_property'))}")
            print(f"    Has ga_property_id: {bool(site.get('ga_property_id'))}")
            print(f"    Has service_account_data: {bool(site.get('service_account_data'))}")
            print()
        
        # Test with the first site that has configuration
        test_site = None
        for site in sites_response.data:
            if site.get('domain_property') and site.get('ga_property_id'):
                test_site = site
                break
        
        if not test_site:
            print("❌ No sites with configuration found")
            return
        
        site_id = test_site['id']
        print(f"🧪 Testing with site ID: {site_id}, Domain: {test_site['domain']}")
        
        # Show current configuration
        print(f"📋 Current configuration:")
        print(f"  - domain_property: {test_site.get('domain_property')}")
        print(f"  - ga_property_id: {test_site.get('ga_property_id')}")
        print(f"  - has service_account_data: {bool(test_site.get('service_account_data'))}")
        print(f"  - homepage: {test_site.get('homepage')}")
        print(f"  - wp_api_key: {test_site.get('wp_api_key')}")
        
        # Test the update operation (what clear data does)
        print(f"\n🔄 Testing update operation...")
        
        update_response = client.table('sites').update({
            'last_updated': datetime.now().isoformat()
        }).eq('id', site_id).execute()
        
        print(f"✅ Update response: {update_response.data}")
        
        # Check the site after update
        verify_response = client.table('sites').select('*').eq('id', site_id).execute()
        
        if verify_response.data:
            updated_site = verify_response.data[0]
            print(f"\n📋 Configuration after update:")
            print(f"  - domain_property: {updated_site.get('domain_property')}")
            print(f"  - ga_property_id: {updated_site.get('ga_property_id')}")
            print(f"  - has service_account_data: {bool(updated_site.get('service_account_data'))}")
            print(f"  - homepage: {updated_site.get('homepage')}")
            print(f"  - wp_api_key: {updated_site.get('wp_api_key')}")
            
            # Check if configuration was preserved
            config_preserved = (
                updated_site.get('domain_property') == test_site.get('domain_property') and
                updated_site.get('ga_property_id') == test_site.get('ga_property_id') and
                bool(updated_site.get('service_account_data')) == bool(test_site.get('service_account_data'))
            )
            
            if config_preserved:
                print(f"\n✅ Configuration was preserved!")
            else:
                print(f"\n❌ Configuration was NOT preserved!")
                print(f"   This indicates there might be a database trigger or constraint issue")
        else:
            print(f"\n❌ Could not verify site after update")
        
        # Test data deletion from other tables
        print(f"\n🗑️ Testing data deletion from other tables...")
        tables_to_check = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
        
        for table in tables_to_check:
            try:
                count_response = client.table(table).select('id').eq('site_id', site_id).execute()
                count = len(count_response.data) if count_response.data else 0
                print(f"  - {table}: {count} records")
            except Exception as e:
                print(f"  - {table}: Error - {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 Testing Clear Site Data Operation")
    print("=" * 50)
    test_clear_data_operation()
