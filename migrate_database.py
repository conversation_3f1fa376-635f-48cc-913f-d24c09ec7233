#!/usr/bin/env python3
"""
Database Migration Script
Adds configuration columns to the sites table
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import settings
from supabase import create_client
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """Add configuration columns to sites table"""
    
    if not settings.supabase_url or not settings.supabase_key:
        logger.error("Supabase credentials not configured")
        return False
    
    try:
        # Create Supabase client
        client = create_client(settings.supabase_url, settings.supabase_key)
        
        logger.info("Starting database migration...")
        
        # SQL to add missing columns
        migration_sql = """
        -- Add configuration columns to sites table
        ALTER TABLE sites 
        ADD COLUMN IF NOT EXISTS domain_property TEXT,
        ADD COLUMN IF NOT EXISTS ga_property_id TEXT,
        ADD COLUMN IF NOT EXISTS service_account_data JSONB,
        ADD COLUMN IF NOT EXISTS homepage TEXT,
        ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();
        
        -- Update existing sites to have a last_updated timestamp
        UPDATE sites 
        SET last_updated = created_at 
        WHERE last_updated IS NULL;
        """
        
        # Execute migration
        logger.info("Executing migration SQL...")
        response = client.rpc('exec_sql', {'sql': migration_sql}).execute()
        
        if response.data:
            logger.info("✅ Migration completed successfully")
        else:
            logger.warning("Migration may have completed (no data returned)")
        
        # Verify the migration by checking table structure
        logger.info("Verifying migration...")
        
        # Try to select from sites table with new columns
        test_response = client.table('sites').select('id, domain, domain_property, ga_property_id, homepage, last_updated').limit(1).execute()
        
        if test_response.data is not None:
            logger.info("✅ Migration verification successful - new columns are accessible")
            
            # Show current sites
            sites_response = client.table('sites').select('*').execute()
            if sites_response.data:
                logger.info(f"Found {len(sites_response.data)} existing sites:")
                for site in sites_response.data:
                    config_status = "Configured" if site.get('domain_property') else "Needs Configuration"
                    logger.info(f"  - {site.get('domain', 'Unknown')}: {config_status}")
            
            return True
        else:
            logger.error("❌ Migration verification failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        logger.exception("Full error details:")
        return False

def check_current_schema():
    """Check current table schema"""
    try:
        client = create_client(settings.supabase_url, settings.supabase_key)
        
        # Try to get a sample record to see current columns
        response = client.table('sites').select('*').limit(1).execute()
        
        if response.data and len(response.data) > 0:
            sample_site = response.data[0]
            logger.info("Current sites table columns:")
            for key in sample_site.keys():
                value = sample_site[key]
                logger.info(f"  - {key}: {type(value).__name__} = {value}")
        else:
            logger.info("No sites found in database")
            
    except Exception as e:
        logger.error(f"Error checking schema: {e}")

if __name__ == "__main__":
    print("🔧 Database Migration Tool")
    print("=" * 50)
    
    print("\n1. Checking current schema...")
    check_current_schema()
    
    print("\n2. Running migration...")
    success = migrate_database()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("\nNext steps:")
        print("1. Restart your API server")
        print("2. Try updating site configuration again")
        print("3. The 'Edit Configuration' feature should now work")
    else:
        print("\n❌ Migration failed!")
        print("\nManual steps:")
        print("1. Open your Supabase dashboard")
        print("2. Go to SQL Editor")
        print("3. Run the SQL from database_migration.sql")
        print("4. Restart your API server")
