"""
FastAPI application setup
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, HTMLResponse
import threading
import time

from src.api.routes import router
from src.config.settings import settings, ensure_directories
from src.utils.logging import get_logger

logger = get_logger(__name__)


def start_cleanup_background_task():
    """Start background cleanup task for old files and directories"""
    def cleanup_worker():
        while True:
            try:
                from src.utils.file_utils import cleanup_old_temp_files, cleanup_old_directories
                from src.api.routes import task_progress

                # Clean up old temporary files (older than 24 hours)
                cleanup_old_temp_files(max_age_hours=24)

                # Clean up old session directories (older than 48 hours)
                cleanup_old_directories(settings.reports_dir, max_age_hours=48)

                # Clean up old task progress entries (older than 24 hours)
                task_progress.cleanup_old_tasks(max_age_hours=24)

                logger.info("Background cleanup completed")

            except Exception as e:
                logger.error(f"Error in background cleanup: {e}")

            # Sleep for 6 hours before next cleanup
            time.sleep(6 * 3600)

    # Start cleanup thread as daemon
    cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
    cleanup_thread.start()
    logger.info("Background cleanup task started")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""

    # Ensure required directories exist
    ensure_directories()

    # Start background cleanup task
    start_cleanup_background_task()
    
    # Create FastAPI app
    app = FastAPI(
        title="SEO Data Analysis API",
        description="API for crawling websites, fetching GSC/GA data, and generating SEO reports with Supabase integration.",
        version="2.0.0",
        debug=settings.debug
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Allow all origins for now - will work on Render
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Mount static files (for serving the HTML dashboard)
    app.mount("/static", StaticFiles(directory="public"), name="static")
    
    # Include API routes
    app.include_router(router)
    
    # Serve the main HTML file at the root
    @app.get("/")
    async def serve_dashboard():
        """Serve the main dashboard HTML file"""
        return FileResponse("public/index.html")
    
    # API Documentation endpoint
    @app.get("/api-docs", response_class=HTMLResponse)
    async def serve_api_docs():
        """Serve API documentation"""
        try:
            with open("API_DOCUMENTATION.md", "r", encoding="utf-8") as f:
                content = f.read()

            # Convert markdown to HTML
            html_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>SEO Site Manager - API Documentation</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css">
                <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/5.1.1/marked.min.js"></script>
                <style>
                    body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
                    .container {{ max-width: 1200px; }}
                    pre {{ background-color: #f8f9fa; padding: 1rem; border-radius: 0.375rem; }}
                    code {{ background-color: #f8f9fa; padding: 0.2rem 0.4rem; border-radius: 0.25rem; }}
                    h1, h2, h3 {{ color: #0d6efd; }}
                    .back-link {{ position: fixed; top: 20px; right: 20px; z-index: 1000; }}
                </style>
            </head>
            <body>
                <a href="/" class="btn btn-primary back-link">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
                <div class="container py-4">
                    <div id="content"></div>
                </div>
                <script>
                    const markdownContent = {repr(content)};
                    document.getElementById('content').innerHTML = marked.parse(markdownContent);
                    hljs.highlightAll();
                </script>
            </body>
            </html>
            """
            return html_content
        except FileNotFoundError:
            return HTMLResponse(content="""
            <html>
            <head><title>API Documentation</title></head>
            <body>
                <h1>API Documentation</h1>
                <p>Documentation file not found. Please check API_DOCUMENTATION.md exists.</p>
                <a href="/">Back to Dashboard</a>
            </body>
            </html>
            """, status_code=404)

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint for deployment monitoring"""
        import os
        return {
            "status": "healthy",
            "version": "2.0.0",
            "environment": "production" if os.getenv("RENDER") else "development",
            "supabase_configured": bool(settings.supabase_url and settings.supabase_key),
            "directories_exist": {
                "reports": os.path.exists("reports"),
                "temp": os.path.exists("temp"),
                "public": os.path.exists("public")
            }
        }
    
    logger.info("FastAPI application created successfully")
    return app


# Create the app instance
app = create_app()
