#!/usr/bin/env python3
"""
Test script to validate the enhanced report generation functionality
"""
import os
import sys
import pandas as pd
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import only what we need to avoid FastAPI initialization
import importlib.util

# Load ReportService module directly
spec = importlib.util.spec_from_file_location("report_service", "src/services/report_service.py")
report_service_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(report_service_module)
ReportService = report_service_module.ReportService

# Load CrawlResult schema directly
spec = importlib.util.spec_from_file_location("schemas", "src/models/schemas.py")
schemas_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(schemas_module)
CrawlResult = schemas_module.CrawlResult

def create_sample_data():
    """Create sample data for testing"""
    
    # Sample crawl results
    crawl_results = [
        CrawlResult(
            url="https://example.com/",
            title="Home Page - Example Site",
            description="This is the home page of our example website",
            h1="Welcome to Example Site",
            text="This is the main content of the home page with lots of useful information about our services."
        ),
        CrawlResult(
            url="https://example.com/about/",
            title="About Us - Example Site",
            description="Learn more about our company and mission",
            h1="About Our Company",
            text="We are a leading company in our industry with years of experience providing excellent services."
        ),
        CrawlResult(
            url="https://example.com/services/",
            title="Our Services - Example Site", 
            description="Discover the range of services we offer",
            h1="Our Professional Services",
            text="We offer a comprehensive range of professional services to meet all your business needs."
        )
    ]
    
    # Sample GSC keywords data
    keywords_data = [
        {"page": "https://example.com/", "query": "example site", "clicks": 150, "impressions": 2000, "ctr": 0.075, "position": 3.2, "Month": "2024-01"},
        {"page": "https://example.com/", "query": "home page", "clicks": 89, "impressions": 1200, "ctr": 0.074, "position": 4.1, "Month": "2024-01"},
        {"page": "https://example.com/about/", "query": "about company", "clicks": 45, "impressions": 800, "ctr": 0.056, "position": 5.3, "Month": "2024-01"},
        {"page": "https://example.com/services/", "query": "professional services", "clicks": 78, "impressions": 1500, "ctr": 0.052, "position": 6.1, "Month": "2024-01"},
        {"page": "https://example.com/", "query": "example site", "clicks": 165, "impressions": 2200, "ctr": 0.075, "position": 3.0, "Month": "2024-02"},
        {"page": "https://example.com/about/", "query": "about company", "clicks": 52, "impressions": 900, "ctr": 0.058, "position": 5.1, "Month": "2024-02"},
    ]
    keywords_df = pd.DataFrame(keywords_data)
    
    # Sample GA data
    ga_data = [
        {"pagePath": "/", "screenPageViews": 1250, "activeUsers": 890, "Month": "2024-01"},
        {"pagePath": "/about/", "screenPageViews": 456, "activeUsers": 320, "Month": "2024-01"},
        {"pagePath": "/services/", "screenPageViews": 678, "activeUsers": 445, "Month": "2024-01"},
        {"pagePath": "/", "screenPageViews": 1380, "activeUsers": 950, "Month": "2024-02"},
        {"pagePath": "/about/", "screenPageViews": 502, "activeUsers": 365, "Month": "2024-02"},
    ]
    ga_df = pd.DataFrame(ga_data)
    
    # Sample internal links data
    internal_links_data = [
        {
            "source_wp_post_id": 1,
            "source_page_title": "Home Page",
            "source_page_link": "https://example.com/",
            "target_hyperlink": "https://example.com/about/",
            "anchor_text": "learn more about us",
            "link_type": "internal",
            "source_post_type": "page",
            "target_page_title_if_internal": "About Us",
            "relevance_score": 0.85
        },
        {
            "source_wp_post_id": 2,
            "source_page_title": "About Us",
            "source_page_link": "https://example.com/about/",
            "target_hyperlink": "https://example.com/services/",
            "anchor_text": "our services",
            "link_type": "internal",
            "source_post_type": "page",
            "target_page_title_if_internal": "Our Services",
            "relevance_score": 0.92
        }
    ]
    internal_links_df = pd.DataFrame(internal_links_data)
    
    # Empty traffic df (will be generated from keywords)
    traffic_df = pd.DataFrame()
    
    return crawl_results, keywords_df, traffic_df, internal_links_df, ga_df

def test_data_aggregation():
    """Test the data aggregation methods"""
    print("Testing data aggregation methods...")
    
    report_service = ReportService()
    crawl_results, keywords_df, traffic_df, internal_links_df, ga_df = create_sample_data()
    homepage = "https://example.com"
    
    # Test GSC aggregation
    print("\n1. Testing GSC data aggregation by URL...")
    gsc_total = report_service.aggregate_gsc_data_by_url(keywords_df)
    print(f"GSC aggregation result:\n{gsc_total}")
    print(f"Columns: {list(gsc_total.columns)}")
    
    # Test GA aggregation
    print("\n2. Testing GA data aggregation by URL...")
    ga_total = report_service.aggregate_ga_data_by_url(ga_df, homepage)
    print(f"GA aggregation result:\n{ga_total}")
    print(f"Columns: {list(ga_total.columns)}")
    
    # Test historical traffic
    print("\n3. Testing historical traffic sheet creation...")
    hist_traffic = report_service.create_historical_traffic_sheet(keywords_df)
    print(f"Historical traffic result:\n{hist_traffic}")
    print(f"Columns: {list(hist_traffic.columns)}")
    
    # Test comprehensive data sheet
    print("\n4. Testing comprehensive data sheet creation...")
    data_sheet = report_service.create_comprehensive_data_sheet(crawl_results, keywords_df, ga_df, homepage)
    print(f"Comprehensive data sheet:\n{data_sheet}")
    print(f"Columns: {list(data_sheet.columns)}")
    
    return True

def test_excel_generation():
    """Test the enhanced Excel report generation"""
    print("\nTesting Excel report generation...")
    
    report_service = ReportService()
    crawl_results, keywords_df, traffic_df, internal_links_df, ga_df = create_sample_data()
    homepage = "https://example.com"
    
    # Create output directory
    output_dir = "test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate Excel report
    excel_path = report_service.generate_excel_report(
        crawl_results, keywords_df, traffic_df, internal_links_df, ga_df, output_dir, homepage
    )
    
    print(f"Excel report generated: {excel_path}")
    
    # Verify the file exists
    if os.path.exists(excel_path):
        print("✅ Excel file created successfully")
        
        # Read and verify sheets
        try:
            excel_file = pd.ExcelFile(excel_path)
            sheets = excel_file.sheet_names
            print(f"Sheets in Excel file: {sheets}")
            
            expected_sheets = ['Data', 'Keywords', 'Historical-Traffic', 'Internal-Links', 'GA-Data']
            for sheet in expected_sheets:
                if sheet in sheets:
                    df = pd.read_excel(excel_path, sheet_name=sheet)
                    print(f"✅ {sheet} sheet: {len(df)} rows, columns: {list(df.columns)}")
                else:
                    print(f"❌ Missing sheet: {sheet}")
                    
        except Exception as e:
            print(f"❌ Error reading Excel file: {e}")
            return False
            
    else:
        print("❌ Excel file was not created")
        return False
    
    return True

def main():
    """Main test function"""
    print("🧪 Testing Enhanced Report Generation")
    print("=" * 50)
    
    try:
        # Test data aggregation
        if test_data_aggregation():
            print("\n✅ Data aggregation tests passed")
        else:
            print("\n❌ Data aggregation tests failed")
            return False
        
        # Test Excel generation
        if test_excel_generation():
            print("\n✅ Excel generation tests passed")
        else:
            print("\n❌ Excel generation tests failed")
            return False
            
        print("\n🎉 All tests passed! Enhanced report generation is working correctly.")
        print("\nKey improvements implemented:")
        print("- ✅ Comprehensive Data sheet with merged GSC + GA + crawl data")
        print("- ✅ Proper GSC data aggregation by URL")
        print("- ✅ GA data aggregation by URL")
        print("- ✅ Historical-Traffic sheet with month-by-month analysis")
        print("- ✅ Original worksheet structure and naming")
        print("- ✅ Proper column ordering and data types")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
