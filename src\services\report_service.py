"""
Report generation service
"""
import os
import pandas as pd
from datetime import datetime
from typing import List, Optional, Dict, Any
from urllib.parse import urlparse

from src.models.schemas import CrawlResult
from src.utils.logging import get_logger

logger = get_logger(__name__)


class ReportService:
    """Service for generating various types of reports"""

    def aggregate_gsc_data_by_url(self, keywords_df: pd.DataFrame) -> pd.DataFrame:
        """Aggregate GSC data by URL for the main Data sheet"""
        if keywords_df.empty:
            return pd.DataFrame(columns=['URL', 'GSC Clicks', 'GSC Impressions', 'CTR', 'Position'])

        # Ensure we have the right column names (handle both old and new formats)
        df = keywords_df.copy()

        # Standardize column names
        column_mapping = {
            'page': 'URL',
            'query': 'Keyword',
            'clicks': 'Clicks',
            'impressions': 'Impressions',
            'ctr': 'CTR',
            'position': 'Position'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})

        # Aggregate by URL
        gsc_total_df = (
            df.groupby('URL', as_index=False)
              .agg({'Clicks': 'sum', 'Impressions': 'sum'})
        )

        # Calculate CTR
        gsc_total_df['CTR'] = gsc_total_df.apply(
            lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
            axis=1
        )

        # Calculate weighted average position
        pos_weight_all = (
            df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
              .groupby('URL', as_index=False)
              .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
        )
        pos_weight_all = pos_weight_all.rename(columns={'Impressions': 'IMP_sum'})

        gsc_total_df = gsc_total_df.merge(
            pos_weight_all[['URL', 'weighted_pos', 'IMP_sum']],
            on='URL',
            how='left'
        ).fillna({'weighted_pos': 0, 'IMP_sum': 1})

        gsc_total_df['Position'] = gsc_total_df.apply(
            lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
            axis=1
        )

        # Clean up and rename columns to match original format
        gsc_total_df = gsc_total_df.drop(columns=['weighted_pos', 'IMP_sum'])
        gsc_total_df = gsc_total_df.rename(columns={
            'Clicks': 'GSC Clicks',
            'Impressions': 'GSC Impressions'
        })

        return gsc_total_df

    def aggregate_ga_data_by_url(self, ga_df: pd.DataFrame, homepage: str) -> pd.DataFrame:
        """Aggregate GA data by URL for the main Data sheet"""
        if ga_df.empty:
            return pd.DataFrame(columns=['URL', 'Google Analytics Page Views', 'Active Users'])

        df = ga_df.copy()

        # Standardize column names
        column_mapping = {
            'pagePath': 'landingPagePlusQueryString',
            'screenPageViews': 'screenPageViews',
            'activeUsers': 'activeUsers',
            'sessions': 'sessions'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})

        # Handle different possible path column names
        path_column = None
        for col in ['landingPagePlusQueryString', 'pagePath', 'page']:
            if col in df.columns:
                path_column = col
                break

        if not path_column:
            logger.warning("No valid path column found in GA data")
            return pd.DataFrame(columns=['URL', 'Google Analytics Page Views', 'Active Users'])

        # Keep only valid paths (those starting with "/")
        df = df[df[path_column].str.startswith('/')].copy()

        # Cast metrics to int BEFORE grouping
        if 'screenPageViews' in df.columns:
            df['screenPageViews'] = df['screenPageViews'].astype(int)
        if 'activeUsers' in df.columns:
            df['activeUsers'] = df['activeUsers'].astype(int)

        # Build the full-URL column so it matches crawl data
        base = homepage.rstrip('/')
        df['URL'] = base + df[path_column]

        # Aggregate per-URL for the "Data" sheet
        agg_columns = {}
        if 'screenPageViews' in df.columns:
            agg_columns['screenPageViews'] = 'sum'
        if 'activeUsers' in df.columns:
            agg_columns['activeUsers'] = 'sum'

        if not agg_columns:
            logger.warning("No valid metrics found in GA data")
            return pd.DataFrame(columns=['URL', 'Google Analytics Page Views', 'Active Users'])

        ga_total_df = (
            df.groupby('URL', as_index=False)
             .agg(agg_columns)
             .rename(columns={
                 'screenPageViews': 'Google Analytics Page Views',
                 'activeUsers': 'Active Users'
             })
        )

        return ga_total_df

    def create_historical_traffic_sheet(self, keywords_df: pd.DataFrame) -> pd.DataFrame:
        """Create Historical-Traffic sheet by aggregating GSC data by URL and Month"""
        if keywords_df.empty:
            return pd.DataFrame(columns=['URL', 'Month', 'Clicks', 'Impressions', 'CTR', 'Position'])

        df = keywords_df.copy()

        # Standardize column names
        column_mapping = {
            'page': 'URL',
            'clicks': 'Clicks',
            'impressions': 'Impressions',
            'position': 'Position'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in df.columns and new_col not in df.columns:
                df = df.rename(columns={old_col: new_col})

        # Aggregate by URL and Month
        hist_traffic_df = (
            df.groupby(['URL', 'Month'], as_index=False)
             .agg({'Clicks': 'sum', 'Impressions': 'sum'})
        )

        # Calculate CTR
        hist_traffic_df['CTR'] = hist_traffic_df.apply(
            lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
            axis=1
        )

        # Calculate weighted average position
        pos_weight = (
            df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
             .groupby(['URL', 'Month'], as_index=False)
             .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
        )
        pos_weight = pos_weight.rename(columns={'Impressions': 'IMP_sum'})

        hist_traffic_df = hist_traffic_df.merge(
            pos_weight[['URL', 'Month', 'weighted_pos', 'IMP_sum']],
            on=['URL', 'Month'],
            how='left'
        ).fillna({'weighted_pos': 0, 'IMP_sum': 1})

        hist_traffic_df['Position'] = hist_traffic_df.apply(
            lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
            axis=1
        )

        hist_traffic_df = hist_traffic_df.drop(columns=['weighted_pos', 'IMP_sum'])

        return hist_traffic_df

    def create_comprehensive_data_sheet(self, crawl_results: List[CrawlResult],
                                      keywords_df: pd.DataFrame, ga_df: pd.DataFrame,
                                      homepage: str) -> pd.DataFrame:
        """Create the comprehensive Data sheet that merges all data sources"""

        # Create crawl data DataFrame
        if crawl_results:
            crawl_data = []
            for result in crawl_results:
                crawl_data.append({
                    'URL': result.url,
                    'Page Content': result.text if result.text else '',
                    'SEO Title': result.title,
                    'Title Length': len(result.title) if result.title else 0,
                    'Meta Description': result.description,
                    'H1': result.h1,
                    'Focus Keyword': '',  # Placeholder - can be populated from WordPress API
                    'Page Type': '',      # Placeholder - can be populated from WordPress API
                    'Topic': ''           # Placeholder - can be populated from WordPress API
                })
            crawl_df = pd.DataFrame(crawl_data)
        else:
            crawl_df = pd.DataFrame(columns=[
                'URL', 'Page Content', 'SEO Title', 'Title Length',
                'Meta Description', 'H1', 'Focus Keyword', 'Page Type', 'Topic'
            ])

        # Aggregate GSC data by URL
        gsc_total_df = self.aggregate_gsc_data_by_url(keywords_df)

        # Aggregate GA data by URL
        ga_total_df = self.aggregate_ga_data_by_url(ga_df, homepage)

        # Merge all data sources
        data_df = crawl_df.merge(gsc_total_df, on='URL', how='left')
        data_df = data_df.merge(ga_total_df, on='URL', how='left')

        # Fill NaNs with 0 for numeric metrics
        numeric_columns = ['GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views', 'Active Users']
        for col in numeric_columns:
            if col in data_df.columns:
                data_df[col] = data_df[col].fillna(0)
                data_df[col] = data_df[col].astype(int)

        # Reorder columns to match original format
        data_columns = [
            'URL',
            'GSC Clicks',
            'GSC Impressions',
            'Google Analytics Page Views',
            'Focus Keyword',
            'Page Type',
            'Topic',
            'Page Content',
            'SEO Title',
            'Title Length',
            'Meta Description',
            'H1'
        ]

        # Only include columns that exist
        data_df = data_df[[col for col in data_columns if col in data_df.columns]]

        return data_df

    def generate_excel_report(self, crawl_results: List[CrawlResult],
                            keywords_df: pd.DataFrame, traffic_df: pd.DataFrame,
                            internal_links_df: pd.DataFrame, ga_df: pd.DataFrame,
                            output_dir: str, homepage: str = None) -> str:
        """Generate comprehensive Excel report matching original main.py format"""
        logger.info("Generating Excel report...")

        # Create unique Excel file path to prevent conflicts
        if homepage:
            from src.utils.file_utils import get_unique_excel_filename
            filename = get_unique_excel_filename(homepage, "analysis_report")
            excel_path = os.path.join(output_dir, filename)
        else:
            # Fallback for cases without homepage
            import uuid
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            unique_id = str(uuid.uuid4())[:8]
            excel_path = os.path.join(output_dir, f'final_report_{timestamp}_{unique_id}.xlsx')

        # Use xlsxwriter engine to match original format
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:

            # 1. Data sheet - comprehensive merged data (main sheet)
            if homepage:
                data_df = self.create_comprehensive_data_sheet(crawl_results, keywords_df, ga_df, homepage)
                data_df.to_excel(writer, sheet_name='Data', index=False)
                logger.info(f"Added comprehensive Data sheet with {len(data_df)} pages")
            else:
                logger.warning("No homepage provided - skipping comprehensive Data sheet")

            # 2. Keywords sheet - raw GSC keyword data
            if not keywords_df.empty:
                keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                logger.info(f"Added {len(keywords_df)} keywords to Excel report")
            else:
                # Create empty sheet with headers
                empty_keywords = pd.DataFrame(columns=['URL', 'Keyword', 'Clicks', 'Impressions', 'CTR', 'Position', 'Month'])
                empty_keywords.to_excel(writer, sheet_name='Keywords', index=False)
                logger.info("Added empty Keywords sheet")

            # 3. Historical-Traffic sheet - aggregated traffic by URL and month
            hist_traffic_df = self.create_historical_traffic_sheet(keywords_df)
            hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
            logger.info(f"Added Historical-Traffic sheet with {len(hist_traffic_df)} records")

            # 4. Internal-Links sheet
            if not internal_links_df.empty:
                internal_links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
                logger.info(f"Added {len(internal_links_df)} internal links to Excel report")
            else:
                # Create empty sheet with headers
                empty_links = pd.DataFrame(columns=[
                    'source_wp_post_id', 'source_page_title', 'source_page_link',
                    'target_hyperlink', 'anchor_text', 'link_type', 'source_post_type',
                    'target_page_title_if_internal', 'relevance_score'
                ])
                empty_links.to_excel(writer, sheet_name='Internal-Links', index=False)
                logger.info("Added empty Internal-Links sheet")

            # 5. GA-Data sheet - Google Analytics data
            if not ga_df.empty:
                ga_df.to_excel(writer, sheet_name='GA-Data', index=False)
                logger.info(f"Added {len(ga_df)} GA records to Excel report")
            else:
                # Create empty sheet with headers
                empty_ga = pd.DataFrame(columns=[
                    'pagePath', 'sessions', 'screenPageViews', 'bounceRate',
                    'averageSessionDuration', 'Month'
                ])
                empty_ga.to_excel(writer, sheet_name='GA-Data', index=False)
                logger.info("Added empty GA-Data sheet")

        logger.info(f"Excel report generated: {excel_path}")
        return excel_path
    
    def generate_csv_reports(self, crawl_results: List[CrawlResult], 
                           keywords_df: pd.DataFrame, traffic_df: pd.DataFrame,
                           internal_links_df: pd.DataFrame, ga_df: pd.DataFrame,
                           output_dir: str) -> List[str]:
        """Generate individual CSV reports"""
        logger.info("Generating CSV reports...")
        csv_files = []
        
        # Pages CSV
        if crawl_results:
            pages_data = []
            for result in crawl_results:
                pages_data.append({
                    'URL': result.url,
                    'Title': result.title,
                    'Description': result.description,
                    'H1': result.h1,
                    'Content Length': len(result.text) if result.text else 0,
                    'Word Count': len(result.text.split()) if result.text else 0
                })
            
            pages_df = pd.DataFrame(pages_data)
            pages_csv = os.path.join(output_dir, 'pages.csv')
            pages_df.to_csv(pages_csv, index=False)
            csv_files.append(pages_csv)
        
        # Keywords CSV
        if not keywords_df.empty:
            keywords_csv = os.path.join(output_dir, 'keywords.csv')
            keywords_df.to_csv(keywords_csv, index=False)
            csv_files.append(keywords_csv)
        
        # Traffic CSV
        if not traffic_df.empty:
            traffic_csv = os.path.join(output_dir, 'traffic.csv')
            traffic_df.to_csv(traffic_csv, index=False)
            csv_files.append(traffic_csv)
        
        # Internal links CSV
        if not internal_links_df.empty:
            links_csv = os.path.join(output_dir, 'internal_links.csv')
            internal_links_df.to_csv(links_csv, index=False)
            csv_files.append(links_csv)
        
        # Analytics CSV
        if not ga_df.empty:
            ga_csv = os.path.join(output_dir, 'analytics.csv')
            ga_df.to_csv(ga_csv, index=False)
            csv_files.append(ga_csv)
        
        logger.info(f"Generated {len(csv_files)} CSV files")
        return csv_files
    
    def generate_summary_report(self, crawl_results: List[CrawlResult], 
                              keywords_df: pd.DataFrame, traffic_df: pd.DataFrame,
                              internal_links_df: pd.DataFrame, ga_df: pd.DataFrame,
                              domain: str) -> Dict[str, Any]:
        """Generate a summary report with key metrics"""
        summary = {
            'domain': domain,
            'analysis_date': datetime.now().isoformat(),
            'metrics': {
                'total_pages': len(crawl_results) if crawl_results else 0,
                'total_keywords': len(keywords_df) if not keywords_df.empty else 0,
                'total_traffic_records': len(traffic_df) if not traffic_df.empty else 0,
                'total_internal_links': len(internal_links_df) if not internal_links_df.empty else 0,
                'total_ga_records': len(ga_df) if not ga_df.empty else 0
            }
        }
        
        # Add top performing pages if available
        if not traffic_df.empty and 'clicks' in traffic_df.columns:
            top_pages = traffic_df.nlargest(10, 'clicks')[['page', 'clicks']].to_dict('records')
            summary['top_pages_by_clicks'] = top_pages
        
        # Add top keywords if available
        if not keywords_df.empty and 'clicks' in keywords_df.columns:
            top_keywords = keywords_df.nlargest(10, 'clicks')[['query', 'clicks']].to_dict('records')
            summary['top_keywords_by_clicks'] = top_keywords
        
        return summary
